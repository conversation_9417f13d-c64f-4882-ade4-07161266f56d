import { DocumentType, ProcessingOptions, ProcessingResult } from '../../shared/types/Document';
import { logger } from '../utils/logger';
import { DocumentProcessorFactory, documentProcessorFactory } from './DocumentProcessorFactory';
import { OCRWorker } from '../workers/OCRWorker';

export interface OCRPipelineOptions extends ProcessingOptions {
  useOCRWorker?: boolean;
  ocrWorkerTimeout?: number;
  fallbackToRegularProcessing?: boolean;
  enhanceImagesForOCR?: boolean;
  detectScannedDocuments?: boolean;
}

export interface OCRPipelineResult extends ProcessingResult {
  ocrUsed: boolean;
  isScannedDocument: boolean;
  ocrConfidence: number | undefined;
  processingMethod: 'regular' | 'ocr' | 'hybrid';
}

/**
 * OCR-enabled document processing pipeline
 * Intelligently determines when to use OCR and coordinates between different processors
 */
export class OCRDocumentPipeline {
  private readonly ocrWorker: OCRWorker;
  private readonly processorFactory: DocumentProcessorFactory;

  constructor() {
    this.ocrWorker = new OCRWorker({
      maxWorkers: 3,
      cacheEnabled: true,
      cacheTTL: 3600,
      defaultLanguage: 'eng',
      supportedLanguages: ['eng', 'spa', 'fra', 'deu'],
    });
    this.processorFactory = documentProcessorFactory;
  }

  /**
   * Initialize the OCR pipeline
   */
  public initialize(): void {
    this.ocrWorker.start();
    logger.info('OCR Document Pipeline initialized');
  }

  /**
   * Process document with intelligent OCR detection and fallback
   */
  public async processDocument(
    buffer: Buffer,
    documentType: DocumentType,
    options: OCRPipelineOptions = {
      extractText: true,
      extractImages: false,
      extractTables: false,
      detectFormFields: false,
      performOCR: false,
      enhanceImages: false,
      preserveFormatting: false,
      useOCRWorker: false,
      enhanceImagesForOCR: false,
    }
  ): Promise<OCRPipelineResult> {
    const startTime = Date.now();

    try {
      logger.info('Starting OCR-enabled document processing', {
        documentType,
        bufferSize: buffer.length,
        options,
      });

      // Determine if document is likely scanned
      const isScannedDocument = options.detectScannedDocuments
        ? await this.detectScannedDocument(buffer, documentType)
        : false;

      // Determine processing strategy
      const shouldUseOCR = this.shouldUseOCR(documentType, options, isScannedDocument);

      let processingResult: ProcessingResult;
      let processingMethod: 'regular' | 'ocr' | 'hybrid';
      let ocrUsed = false;
      let ocrConfidence: number | undefined;

      if (shouldUseOCR && options.useOCRWorker && this.ocrWorker.isWorkerRunning()) {
        // Use OCR worker for processing
        try {
          processingResult = await this.processWithOCRWorker(buffer, documentType, options);
          processingMethod = 'ocr';
          ocrUsed = true;
          ocrConfidence = this.extractOCRConfidence(processingResult);
        } catch (ocrError) {
          logger.warn('OCR worker processing failed, falling back to regular processing', {
            ocrError,
          });

          if (options.fallbackToRegularProcessing !== false) {
            processingResult = await this.processWithRegularProcessor(
              buffer,
              documentType,
              options
            );
            processingMethod = 'regular';
          } else {
            throw ocrError;
          }
        }
      } else if (shouldUseOCR) {
        // Use hybrid approach with regular processor that has OCR capabilities
        processingResult = await this.processWithHybridApproach(buffer, documentType, options);
        processingMethod = 'hybrid';
        ocrUsed = true;
        ocrConfidence = this.extractOCRConfidence(processingResult);
      } else {
        // Use regular processing
        processingResult = await this.processWithRegularProcessor(buffer, documentType, options);
        processingMethod = 'regular';
      }

      const totalProcessingTime = Date.now() - startTime;

      logger.info('OCR-enabled document processing completed', {
        processingMethod,
        ocrUsed,
        isScannedDocument,
        ocrConfidence,
        totalProcessingTime,
        success: processingResult.success,
      });

      return {
        ...processingResult,
        ocrUsed,
        isScannedDocument,
        ocrConfidence,
        processingMethod,
        processingTime: totalProcessingTime,
      };
    } catch (error) {
      logger.error('OCR document pipeline processing failed', { error });

      return {
        documentId: '',
        success: false,
        error: error.message,
        content: {
          text: '',
          pages: [],
          formFields: [],
          images: [],
          tables: [],
        },
        processingTime: Date.now() - startTime,
        confidence: 0,
        warnings: [
          `Pipeline processing failed: ${error instanceof Error ? error.message : String(error)}`,
        ],
        ocrUsed: false,
        isScannedDocument: false,
        ocrConfidence: undefined,
        processingMethod: 'regular',
      };
    }
  }

  /**
   * Detect if document is likely scanned (image-based)
   */
  private async detectScannedDocument(
    buffer: Buffer,
    documentType: DocumentType
  ): Promise<boolean> {
    if (documentType === DocumentType.IMAGE) {
      return true; // Images are always considered "scanned"
    }

    if (documentType === DocumentType.PDF) {
      try {
        // Try to extract text normally first
        const processor = await this.processorFactory.createProcessor({
          documentType,
          fileSize: buffer.length,
          requiresOCR: false,
        });

        const text = await processor.extractText(buffer, {
          extractText: true,
          extractImages: false,
          extractTables: false,
          detectFormFields: false,
          performOCR: false,
          enhanceImages: false,
          preserveFormatting: false,
        });
        const textLength = text.trim().length;
        const estimatedTextPerPage =
          textLength / Math.max(1, Math.ceil(buffer.length / (1024 * 100))); // Rough estimate

        // If very little text extracted, likely scanned
        return estimatedTextPerPage < 50;
      } catch (error) {
        logger.warn('Failed to detect if PDF is scanned', { error });
        return false;
      }
    }

    return false;
  }

  /**
   * Determine if OCR should be used
   */
  private shouldUseOCR(
    documentType: DocumentType,
    options: OCRPipelineOptions,
    isScannedDocument: boolean
  ): boolean {
    // Explicit OCR request
    if (options.performOCR) {
      return true;
    }

    // Image documents always use OCR
    if (documentType === DocumentType.IMAGE) {
      return true;
    }

    // Scanned documents should use OCR
    if (isScannedDocument) {
      return true;
    }

    // Form field detection on potentially scanned documents
    if (options.detectFormFields && documentType === DocumentType.PDF) {
      return true;
    }

    return false;
  }

  /**
   * Process document using OCR worker
   */
  private async processWithOCRWorker(
    buffer: Buffer,
    documentType: DocumentType,
    options: OCRPipelineOptions
  ): Promise<ProcessingResult> {
    if (documentType === DocumentType.IMAGE) {
      // For images, use OCR worker directly
      const ocrResult = await this.ocrWorker.processImage(buffer, {
        ocrOptions: {
          language: options.ocrLanguage || 'eng',
          minimumConfidence: 60,
        },
        enhanceImage: options.enhanceImagesForOCR !== false,
      });

      return {
        documentId: '',
        success: true,
        content: {
          text: ocrResult.text,
          pages: [
            {
              pageNumber: 1,
              text: ocrResult.text,
              width: 0,
              height: 0,
            },
          ],
          formFields: [],
          images: [],
          tables: [],
        },
        processingTime: 0, // Will be set by caller
        confidence: ocrResult.confidence / 100,
        warnings: [],
      };
    } else {
      // For other document types, fall back to hybrid approach
      return this.processWithHybridApproach(buffer, documentType, options);
    }
  }

  /**
   * Process document using regular processor
   */
  private async processWithRegularProcessor(
    buffer: Buffer,
    documentType: DocumentType,
    options: OCRPipelineOptions
  ): Promise<ProcessingResult> {
    const processor = await this.processorFactory.createProcessor({
      documentType,
      fileSize: buffer.length,
      requiresOCR: false,
      requiresFormFields: options.detectFormFields,
      requiresTableExtraction: options.extractTables,
      requiresImageExtraction: options.extractImages,
    });

    // Initialize processor if it has OCR capabilities
    if ('initialize' in processor && typeof processor.initialize === 'function') {
      await processor.initialize();
    }

    const result = await processor.processDocument(buffer, 'document', options);
    return result.processingResult;
  }

  /**
   * Process document using hybrid approach (regular processor with OCR capabilities)
   */
  private async processWithHybridApproach(
    buffer: Buffer,
    documentType: DocumentType,
    options: OCRPipelineOptions
  ): Promise<ProcessingResult> {
    const processor = await this.processorFactory.createProcessor({
      documentType,
      fileSize: buffer.length,
      requiresOCR: true,
      requiresFormFields: options.detectFormFields,
      requiresTableExtraction: options.extractTables,
      requiresImageExtraction: options.extractImages,
    });

    // Initialize processor if it has OCR capabilities
    if ('initialize' in processor && typeof processor.initialize === 'function') {
      await processor.initialize();
    }

    // Enable OCR in processing options
    const ocrOptions: ProcessingOptions = {
      ...options,
      performOCR: true,
      enhanceImages: options.enhanceImagesForOCR !== false,
    };

    const result = await processor.processDocument(buffer, 'document', ocrOptions);
    return result.processingResult;
  }

  /**
   * Extract OCR confidence from processing result
   */
  private extractOCRConfidence(result: ProcessingResult): number | undefined {
    // Look for OCR-related extracted data to get confidence
    const ocrData = result.content.pages?.[0];
    return ocrData ? result.confidence : undefined;
  }

  /**
   * Cleanup pipeline resources
   */
  public async cleanup(): Promise<void> {
    await this.ocrWorker.stop();
    logger.info('OCR Document Pipeline cleaned up');
  }
}
