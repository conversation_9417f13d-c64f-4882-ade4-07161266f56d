import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create extracted_data table
  await knex.schema.createTable('extracted_data', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Foreign key to documents table
    table.string('document_id', 36).notNullable().comment('Reference to documents table');
    table.foreign('document_id').references('id').inTable('documents').onDelete('CASCADE');

    // Data classification and content
    table.string('data_type', 100).notNullable().comment('Type of extracted data (text, table, form_field, image, etc.)');
    table.text('content').notNullable().comment('The actual extracted content');
    table.decimal('confidence', 5, 4).notNullable().comment('Extraction confidence score (0-1)');
    table.string('extraction_method', 100).notNullable().comment('Method used for extraction (ocr, pdf_text, ai_analysis, etc.)');

    // Coordinate and positioning information
    table.inates').comment('Position coordinates within the document (x, y, width, height, page)');
    table.integer('page_number').unsigned().comment('Page number where data was found');
    table.json('bounding_box').comment('Detailed bounding box information');

    // Vector embeddings for semantic search
    table.binary('embeddings').comment('Vector embeddings for semantic search (compressed)');
    table.integer('embedding_dimension').unsigned().comment('Dimension of the embedding vector');
    table.string('embedding_model', 100).comment('Model used to generate embeddings');

    // Metadata and context
    table.json('metadata').comment('Additional metadata about the extraction');
    table.text('context').comment('Surrounding context or related information');
    table.string('language', 10).comment('Detected language of the extracted content');
    table.json('formatting').comment('Original formatting information (font, size, style, etc.)');

    // Processing information
    table.string('processor_version', 50).comment('Version of the processor that extracted this data');
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Extraction timestamp');
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable().comment('Last update timestamp');
    table.boolean('is_validated').defaultTo(false).notNullable().comment('Whether extraction has been manually validated');
    table.timestamp('validated_at').comment('When extraction was validated');
    table.string('validated_by', 100).comment('Who validated the extraction');

    // Quality and reliability metrics
    table.decimal('quality_score', 5, 4).comment('Overall quality score of the extraction');
    table.json('quality_metrics').comment('Detailed quality metrics');
    table.boolean('needs_review').defaultTo(false).notNullable().comment('Whether extraction needs manual review');
    table.text('review_notes').comment('Notes from manual review');

    // Relationships and dependencies
    table.string('parent_extraction_id', 36).comment('Parent extraction if this is a sub-extraction');
    table.foreign('parent_extraction_id').references('id').inTable('extracted_data').onDelete('SET NULL');
    table.json('related_extractions').comment('Array of related extraction IDs');

    // Constraints
    table.check('confidence >= 0 AND confidence <= 1', [], 'confidence_range');
    table.check('quality_score IS NULL OR (quality_score >= 0 AND quality_score <= 1)', [], 'quality_score_range');
    table.check('page_number > 0', [], 'page_number_positive');
    table.check('embedding_dimension > 0', [], 'embedding_dimension_positive');
    table.check("data_type IN ('text', 'table', 'form_field', 'image', 'chart', 'signature', 'barcode', 'qr_code', 'entity', 'relationship', 'other')", [], 'valid_data_type');
    table.check("extraction_method IN ('ocr', 'pdf_text', 'pdf_form', 'ai_analysis', 'nlp', 'computer_vision', 'manual', 'other')", [], 'valid_extraction_method');

    // Indexes for performance
    table.index(['document_id'], 'idx_extracted_data_document');
    table.index(['data_type'], 'idx_extracted_data_type');
    table.index(['confidence'], 'idx_extracted_data_confidence');
    table.index(['extraction_method'], 'idx_extracted_data_method');
    table.index(['created_at'], 'idx_extracted_data_created_at');
    table.index(['is_validated'], 'idx_extracted_data_validated');
    table.index(['needs_review'], 'idx_extracted_data_review');
    table.index(['page_number'], 'idx_extracted_data_page');
    table.index(['language'], 'idx_extracted_data_language');
    table.index(['parent_extraction_id'], 'idx_extracted_data_parent');

    // Composite indexes for common queries
    table.index(['document_id', 'data_type'], 'idx_extracted_data_doc_type');
    table.index(['document_id', 'page_number'], 'idx_extracted_data_doc_page');
    table.index(['confidence', 'needs_review'], 'idx_extracted_data_confidence_review');
    table.index(['data_type', 'extraction_method'], 'idx_extracted_data_type_method');
    table.index(['is_validated', 'confidence'], 'idx_extracted_data_validated_confidence');
    table.index(['created_at', 'document_id'], 'idx_extracted_data_created_doc');
  });

  // Create full-text search virtual table for extracted content
  await knex.raw(`
    CREATE VIRTUAL TABLE IF NOT EXISTS extracted_data_fts USING fts5(
      id UNINDEXED,
      document_id UNINDEXED,
      content,
      context,
      metadata,
      content='extracted_data',
      content_rowid='rowid'
    )
  `);

  // Create triggers to keep FTS table in sync
  await knex.raw(`
    CREATE TRIGGER extracted_data_fts_insert AFTER INSERT ON extracted_data BEGIN
      INSERT INTO extracted_data_fts(id, document_id, content, context, metadata)
      VALUES (new.id, new.document_id, new.content, new.context, new.metadata);
    END
  `);

  await knex.raw(`
    CREATE TRIGGER extracted_data_fts_delete AFTER DELETE ON extracted_data BEGIN
      DELETE FROM extracted_data_fts WHERE id = old.id;
    END
  `);

  await knex.raw(`
    CREATE TRIGGER extracted_data_fts_update AFTER UPDATE ON extracted_data BEGIN
      DELETE FROM extracted_data_fts WHERE id = old.id;
      INSERT INTO extracted_data_fts(id, document_id, content, context, metadata)
      VALUES (new.id, new.document_id, new.content, new.context, new.metadata);
    END
  `);

  // Create updated_at trigger
  await knex.raw(`
    CREATE TRIGGER update_extracted_data_updated_at
    AFTER UPDATE ON extracted_data
    FOR EACH ROW
    BEGIN
      UPDATE extracted_data SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END
  `);

  // Create view for high-confidence extractions
  await knex.raw(`
    CREATE VIEW high_confidence_extractions AS
    SELECT
      ed.*,
      d.name as document_name,
      d.type as document_type
    FROM extracted_data ed
    JOIN documents d ON ed.document_id = d.id
    WHERE ed.confidence >= 0.8 AND ed.needs_review = 0
  `);

  // Create view for extractions needing review
  await knex.raw(`
    CREATE VIEW extractions_for_review AS
    SELECT
      ed.*,
      d.name as document_name,
      d.type as document_type,
      CASE
        WHEN ed.confidence < 0.5 THEN 'Low Confidence'
        WHEN ed.needs_review = 1 THEN 'Flagged for Review'
        WHEN ed.is_validated = 0 AND ed.confidence < 0.8 THEN 'Needs Validation'
        ELSE 'Other'
      END as review_reason
    FROM extracted_data ed
    JOIN documents d ON ed.document_id = d.id
    WHERE ed.confidence < 0.8 OR ed.needs_review = 1 OR ed.is_validated = 0
    ORDER BY ed.confidence ASC, ed.created_at DESC
  `);

  // Create view for extraction statistics by document
  await knex.raw(`
    CREATE VIEW document_extraction_stats AS
    SELECT
      d.id as document_id,
      d.name as document_name,
      d.type as document_type,
      COUNT(ed.id) as total_extractions,
      COUNT(CASE WHEN ed.is_validated = 1 THEN 1 END) as validated_extractions,
      COUNT(CASE WHEN ed.needs_review = 1 THEN 1 END) as extractions_needing_review,
      AVG(ed.confidence) as avg_confidence,
      MIN(ed.confidence) as min_confidence,
      MAX(ed.confidence) as max_confidence,
      COUNT(DISTINCT ed.data_type) as unique_data_types,
      MAX(ed.created_at) as last_extraction_time
    FROM documents d
    LEFT JOIN extracted_data ed ON d.id = ed.document_id
    GROUP BY d.id, d.name, d.type
  `);

  // Create index on embeddings for vector similarity search (if using extension)
  // Note: This would require a vector similarity extension like sqlite-vss
  // await knex.raw(`CREATE INDEX IF NOT EXISTS idx_extracted_data_embeddings ON extracted_data USING vss(embeddings)`);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS update_extracted_data_updated_at');
  await knex.raw('DROP TRIGGER IF EXISTS extracted_data_fts_update');
  await knex.raw('DROP TRIGGER IF EXISTS extracted_data_fts_delete');
  await knex.raw('DROP TRIGGER IF EXISTS extracted_data_fts_insert');

  // Drop FTS table
  await knex.raw('DROP TABLE IF EXISTS extracted_data_fts');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS document_extraction_stats');
  await knex.raw('DROP VIEW IF EXISTS extractions_for_review');
  await knex.raw('DROP VIEW IF EXISTS high_confidence_extractions');

  // Drop main table (this will also drop all indexes and foreign keys)
  await knex.schema.dropTableIfExists('extracted_data');
}
