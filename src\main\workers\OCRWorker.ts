import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { ImageProcessResult, OCROptions, OCRResult } from '../../shared/types/Document';
import { ImageProcessingOptions, ImageProcessor } from '../services/ImageProcessor';
import { OCREngine, OCREngineConfig } from '../services/OCREngine';
import { logger } from '../utils/logger';

export interface OCRWorkerMessage {
  id: string;
  type: 'ocr' | 'image_process' | 'batch_ocr' | 'shutdown';
  data: any;
}

export interface OCRWorkerResponse {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  processingTime: number;
}

export interface OCRJob {
  id: string;
  type: 'single' | 'batch';
  imageBuffer: Buffer;
  ocrOptions?: Partial<OCROptions>;
  imageOptions?: Partial<ImageProcessingOptions>;
  enhanceImage?: boolean;
}

export interface BatchOCRJob {
  id: string;
  images: Array<{
    buffer: Buffer;
    name?: string;
  }>;
  ocrOptions?: Partial<OCROptions>;
  imageOptions?: Partial<ImageProcessingOptions>;
  enhanceImages?: boolean;
}

/**
 * OCR Worker for background processing
 * Handles OCR operations in a separate thread to prevent UI blocking
 */
export class OCRWorker {
  private worker: Worker | null = null;
  private isRunning = false;
  private readonly messageHandlers = new Map<string, (response: OCRWorkerResponse) => void>();
  private messageId = 0;

  constructor(private config: Partial<OCREngineConfig> = {}) {}

  /**
   * Start the OCR worker
   */
  public start(): void {
    if (this.isRunning) {
      return;
    }

    try {
      this.worker = new Worker(__filename, {
        workerData: { config: this.config },
      });

      this.worker.on('message', (response: OCRWorkerResponse) => {
        const handler = this.messageHandlers.get(response.id);
        if (handler) {
          handler(response);
          this.messageHandlers.delete(response.id);
        }
      });

      this.worker.on('error', error => {
        logger.error('OCR Worker error:', error);
      });

      this.worker.on('exit', code => {
        if (code !== 0) {
          logger.error(`OCR Worker stopped with exit code ${code}`);
        }
        this.isRunning = false;
      });

      this.isRunning = true;
      logger.info('OCR Worker started successfully');
    } catch (error) {
      logger.error('Failed to start OCR Worker:', error);
      throw error;
    }
  }

  /**
   * Process single image with OCR
   */
  public async processImage(
    imageBuffer: Buffer,
    options: {
      ocrOptions?: Partial<OCROptions>;
      imageOptions?: Partial<ImageProcessingOptions>;
      enhanceImage?: boolean;
    } = {}
  ): Promise<OCRResult> {
    if (!this.isRunning || !this.worker) {
      throw new Error('OCR Worker is not running');
    }

    const messageId = this.generateMessageId();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageHandlers.delete(messageId);
        reject(new Error('OCR processing timeout'));
      }, 60000); // 60 second timeout

      this.messageHandlers.set(messageId, (response: OCRWorkerResponse) => {
        clearTimeout(timeout);

        if (response.success) {
          resolve(response.result);
        } else {
          reject(new Error(response.error || 'OCR processing failed'));
        }
      });

      const message: OCRWorkerMessage = {
        id: messageId,
        type: 'ocr',
        data: {
          imageBuffer,
          ocrOptions: options.ocrOptions,
          imageOptions: options.imageOptions,
          enhanceImage: options.enhanceImage,
        },
      };

      this.worker!.postMessage(message);
    });
  }

  /**
   * Process image enhancement only
   */
  public async enhanceImage(
    imageBuffer: Buffer,
    options: Partial<ImageProcessingOptions> = {}
  ): Promise<ImageProcessResult> {
    if (!this.isRunning || !this.worker) {
      throw new Error('OCR Worker is not running');
    }

    const messageId = this.generateMessageId();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageHandlers.delete(messageId);
        reject(new Error('Image processing timeout'));
      }, 30000); // 30 second timeout

      this.messageHandlers.set(messageId, (response: OCRWorkerResponse) => {
        clearTimeout(timeout);

        if (response.success) {
          resolve(response.result);
        } else {
          reject(new Error(response.error || 'Image processing failed'));
        }
      });

      const message: OCRWorkerMessage = {
        id: messageId,
        type: 'image_process',
        data: {
          imageBuffer,
          options,
        },
      };

      this.worker!.postMessage(message);
    });
  }

  /**
   * Process multiple images in batch
   */
  public async batchProcess(
    images: Array<{ buffer: Buffer; name?: string }>,
    options: {
      ocrOptions?: Partial<OCROptions>;
      imageOptions?: Partial<ImageProcessingOptions>;
      enhanceImages?: boolean;
    } = {}
  ): Promise<Array<{ name?: string; result: OCRResult; error?: string }>> {
    if (!this.isRunning || !this.worker) {
      throw new Error('OCR Worker is not running');
    }

    const messageId = this.generateMessageId();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.messageHandlers.delete(messageId);
        reject(new Error('Batch OCR processing timeout'));
      }, 300000); // 5 minute timeout for batch

      this.messageHandlers.set(messageId, (response: OCRWorkerResponse) => {
        clearTimeout(timeout);

        if (response.success) {
          resolve(response.result);
        } else {
          reject(new Error(response.error || 'Batch OCR processing failed'));
        }
      });

      const message: OCRWorkerMessage = {
        id: messageId,
        type: 'batch_ocr',
        data: {
          images,
          ocrOptions: options.ocrOptions,
          imageOptions: options.imageOptions,
          enhanceImages: options.enhanceImages,
        },
      };

      this.worker!.postMessage(message);
    });
  }

  /**
   * Stop the OCR worker
   */
  public async stop(): Promise<void> {
    if (!this.isRunning || !this.worker) {
      return;
    }

    return new Promise(resolve => {
      const messageId = this.generateMessageId();

      this.messageHandlers.set(messageId, () => {
        resolve();
      });

      const message: OCRWorkerMessage = {
        id: messageId,
        type: 'shutdown',
        data: {},
      };

      this.worker!.postMessage(message);

      // Force terminate after 5 seconds
      setTimeout(() => {
        if (this.worker) {
          this.worker.terminate();
          this.isRunning = false;
          resolve();
        }
      }, 5000);
    });
  }

  /**
   * Check if worker is running
   */
  public isWorkerRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `ocr_${++this.messageId}_${Date.now()}`;
  }
}

// Worker thread implementation
if (!isMainThread && parentPort) {
  let ocrEngine: OCREngine;
  let imageProcessor: ImageProcessor;

  // Initialize services
  (async () => {
    try {
      ocrEngine = new OCREngine(workerData.config);
      imageProcessor = new ImageProcessor();
      await ocrEngine.initialize();

      logger.info('OCR Worker thread initialized');
    } catch (error) {
      logger.error('Failed to initialize OCR Worker thread:', error);
      process.exit(1);
    }
  })();

  // Handle messages from main thread
  parentPort.on('message', async (message: OCRWorkerMessage) => {
    const startTime = Date.now();

    try {
      let result: any;

      switch (message.type) {
        case 'ocr':
          result = await handleOCRRequest(message.data);
          break;

        case 'image_process':
          result = await handleImageProcessRequest(message.data);
          break;

        case 'batch_ocr':
          result = await handleBatchOCRRequest(message.data);
          break;

        case 'shutdown':
          await cleanup();
          process.exit(0);
          break;

        default:
          throw new Error(`Unknown message type: ${message.type}`);
      }

      const response: OCRWorkerResponse = {
        id: message.id,
        success: true,
        result,
        processingTime: Date.now() - startTime,
      };

      parentPort!.postMessage(response);
    } catch (error) {
      const response: OCRWorkerResponse = {
        id: message.id,
        success: false,
        error: error.message,
        processingTime: Date.now() - startTime,
      };

      parentPort!.postMessage(response);
    }
  });

  async function handleOCRRequest(data: any): Promise<OCRResult> {
    let imageBuffer = data.imageBuffer;

    // Enhance image if requested
    if (data.enhanceImage) {
      const imageOptions = data.imageOptions || imageProcessor.getOCROptimizedOptions();
      const processResult = await imageProcessor.processImage(imageBuffer, imageOptions);
      imageBuffer = Buffer.from(processResult.processedImage.data);
    }

    // Perform OCR
    return await ocrEngine.extractTextFromImage(imageBuffer, data.ocrOptions);
  }

  async function handleImageProcessRequest(data: any): Promise<ImageProcessResult> {
    return await imageProcessor.processImage(data.imageBuffer, data.options);
  }

  async function handleBatchOCRRequest(
    data: any
  ): Promise<Array<{ name?: string; result: OCRResult; error?: string }>> {
    const results: Array<{ name?: string; result: OCRResult; error?: string }> = [];

    for (const image of data.images) {
      try {
        let imageBuffer = image.buffer;

        // Enhance image if requested
        if (data.enhanceImages) {
          const imageOptions = data.imageOptions || imageProcessor.getOCROptimizedOptions();
          const processResult = await imageProcessor.processImage(imageBuffer, imageOptions);
          imageBuffer = Buffer.from(processResult.processedImage.data);
        }

        // Perform OCR
        const ocrResult = await ocrEngine.extractTextFromImage(imageBuffer, data.ocrOptions);

        results.push({
          name: image.name,
          result: ocrResult,
        });
      } catch (error) {
        results.push({
          name: image.name,
          result: null as any,
          error: error.message,
        });
      }
    }

    return results;
  }

  async function cleanup(): Promise<void> {
    if (ocrEngine) {
      await ocrEngine.cleanup();
    }
  }

  // Handle worker termination
  process.on('SIGTERM', async () => {
    await cleanup();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    await cleanup();
    process.exit(0);
  });
}
