import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create user_sessions table for session management
  await knex.schema.createTable('user_sessions', (table) => {
    // Primary key
    table.string('id', 36).primary().notNullable().comment('UUID primary key');

    // Session identification
    table.string('session_token', 128).unique().notNullable().comment('Unique session token');
    table.string('user_id', 100).notNullable().comment('User identifier');
    table.string('device_id', 100).comment('Device identifier');

    // Session metadata
    table.json('user_agent').comment('User agent information');
    table.string('ip_address', 45).comment('IP address (IPv4 or IPv6)');
    table.string('platform', 50).comment('Operating system platform');
    table.string('app_version', 50).comment('Application version');

    // Session lifecycle
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable().comment('Session creation timestamp');
    table.timestamp('last_activity_at').defaultTo(knex.fn.now()).notNullable().comment('Last activity timestamp');
    table.timestamp('expires_at').notNullable().comment('Session expiration timestamp');
    table.boolean('is_active').defaultTo(true).notNullable().comment('Whether session is active');
    table.timestamp('terminated_at').comment('When session was terminated');
    table.string('termination_reason', 100).comment('Reason for session termination');

    // Session state and preferences
    table.json('session_data').comment('Session-specific data and preferences');
    table.json('ui_state').comment('UI state information');
    table.json('workspace_state').comment('Workspace and document state');
    table.integer('activity_count').defaultTo(0).notNullable().comment('Number of activities in this session');

    // Security and validation
    table.string('csrf_token', 64).comment('CSRF protection token');
    table.json('permissions').comment('Session-specific permissions');
    table.boolean('is_authenticated').defaultTo(false).notNullable().comment('Whether session is authenticated');
    table.timestamp('authenticated_at').comment('When session was authenticated');
    table.string('authentication_method', 50).comment('Method used for authentication');

    // Session analytics
    table.integer('documents_accessed').defaultTo(0).notNullable().comment('Number of documents accessed');
    table.integer('actions_performed').defaultTo(0).notNullable().comment('Number of actions performed');
    table.json('feature_usage').comment('Feature usage statistics');
    table.bigInteger('total_processing_time').defaultTo(0).notNullable().comment('Total processing time in milliseconds');

    // Constraints
    table.check('activity_count >= 0', [], 'activity_count_positive');
    table.check('documents_accessed >= 0', [], 'documents_accessed_positive');
    table.check('actions_performed >= 0', [], 'actions_performed_positive');
    table.check('total_processing_time >= 0', [], 'total_processing_time_positive');
    table.check('expires_at > created_at', [], 'valid_expiration');
    table.check("termination_reason IN ('logout', 'timeout', 'expired', 'forced', 'error', 'other') OR termination_reason IS NULL", [], 'valid_termination_reason');
    table.check("authentication_method IN ('password', 'token', 'biometric', 'sso', 'other') OR authentication_method IS NULL", [], 'valid_auth_method');

    // JSON validation
    table.check("json_valid(user_agent) OR user_agent IS NULL", [], 'valid_user_agent');
    table.check("json_valid(session_data) OR session_data IS NULL", [], 'valid_session_data');
    table.check("json_valid(ui_state) OR ui_state IS NULL", [], 'valid_ui_state');
    table.check("json_valid(workspace_state) OR workspace_state IS NULL", [], 'valid_workspace_state');
    table.check("json_valid(permissions) OR permissions IS NULL", [], 'valid_permissions');
    table.check("json_valid(feature_usage) OR feature_usage IS NULL", [], 'valid_feature_usage');

    // Indexes for performance
    table.index(['session_token'], 'idx_user_sessions_token');
    table.index(['user_id'], 'idx_user_sessions_user');
    table.index(['device_id'], 'idx_user_sessions_device');
    table.index(['created_at'], 'idx_user_sessions_created_at');
    table.index(['last_activity_at'], 'idx_user_sessions_last_activity');
    table.index(['expires_at'], 'idx_user_sessions_expires_at');
    table.index(['is_active'], 'idx_user_sessions_is_active');
    table.index(['is_authenticated'], 'idx_user_sessions_authenticated');
    table.index(['platform'], 'idx_user_sessions_platform');
    table.index(['app_version'], 'idx_user_sessions_app_version');

    // Composite indexes for common queries
    table.index(['user_id', 'is_active'], 'idx_user_sessions_user_active');
    table.index(['is_active', 'expires_at'], 'idx_user_sessions_active_expires');
    table.index(['last_activity_at', 'is_active'], 'idx_user_sessions_activity_active');
    table.index(['created_at', 'user_id'], 'idx_user_sessions_created_user');
  });

  // Create trigger to update last_activity_at on session data updates
  await knex.raw(`
    CREATE TRIGGER update_session_activity
    AFTER UPDATE OF session_data, ui_state, workspace_state, activity_count ON user_sessions
    FOR EACH ROW
    BEGIN
      UPDATE user_sessions
      SET last_activity_at = CURRENT_TIMESTAMP
      WHERE id = NEW.id;
    END
  `);

  // Create trigger to automatically expire sessions
  await knex.raw(`
    CREATE TRIGGER auto_expire_sessions
    AFTER UPDATE OF last_activity_at ON user_sessions
    FOR EACH ROW
    WHEN NEW.expires_at < CURRENT_TIMESTAMP AND NEW.is_active = 1
    BEGIN
      UPDATE user_sessions
      SET is_active = 0,
          terminated_at = CURRENT_TIMESTAMP,
          termination_reason = 'expired'
      WHERE id = NEW.id;
    END
  `);

  // Create view for active sessions
  await knex.raw(`
    CREATE VIEW active_sessions AS
    SELECT
      id,
      session_token,
      user_id,
      device_id,
      platform,
      app_version,
      created_at,
      last_activity_at,
      expires_at,
      is_authenticated,
      activity_count,
      documents_accessed,
      actions_performed,
      (julianday(CURRENT_TIMESTAMP) - julianday(last_activity_at)) * 1440 as minutes_since_activity
    FROM user_sessions
    WHERE is_active = 1 AND expires_at > CURRENT_TIMESTAMP
  `);

  // Create view for session analytics
  await knex.raw(`
    CREATE VIEW session_analytics AS
    SELECT
      user_id,
      COUNT(*) as total_sessions,
      COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_sessions,
      AVG((julianday(COALESCE(terminated_at, CURRENT_TIMESTAMP)) - julianday(created_at)) * 1440) as avg_session_duration_minutes,
      SUM(activity_count) as total_activities,
      SUM(documents_accessed) as total_documents_accessed,
      SUM(actions_performed) as total_actions_performed,
      SUM(total_processing_time) as total_processing_time_ms,
      MAX(last_activity_at) as last_activity,
      COUNT(DISTINCT platform) as unique_platforms,
      COUNT(DISTINCT device_id) as unique_devices
    FROM user_sessions
    GROUP BY user_id
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop triggers first
  await knex.raw('DROP TRIGGER IF EXISTS auto_expire_sessions');
  await knex.raw('DROP TRIGGER IF EXISTS update_session_activity');

  // Drop views
  await knex.raw('DROP VIEW IF EXISTS session_analytics');
  await knex.raw('DROP VIEW IF EXISTS active_sessions');

  // Drop main table
  await knex.schema.dropTableIfExists('user_sessions');
}
