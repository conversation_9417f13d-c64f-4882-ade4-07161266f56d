import { WebContents } from 'electron';
import { logger } from './logger';

export class ConsoleInterceptor {
  private readonly interceptedWebContents = new Set<WebContents>();

  /**
   * Intercept console logs from a WebContents instance
   */
  interceptWebContents(webContents: WebContents): void {
    if (this.interceptedWebContents.has(webContents)) {
      return; // Already intercepted
    }

    this.interceptedWebContents.add(webContents);

    // Intercept console messages
    webContents.on('console-message', (_event, level, message, line, sourceId) => {
      const logLevel = this.mapConsoleLevel(level);

      // Format the message with source information
      let formattedMessage = message;
      if (sourceId && line > 0) {
        // Extract just the filename from the full path
        const filename = sourceId.split('/').pop() || sourceId;
        formattedMessage = `${message} (${filename}:${line})`;
      }

      logger.logFromChromium(logLevel, formattedMessage, {
        originalLevel: level,
        line,
        sourceId,
        url: webContents.getURL(),
        timestamp: new Date().toISOString(),
      });

      // Also log to main process console for debugging
      console.log(`[CHROMIUM ${logLevel.toUpperCase()}] ${formattedMessage}`);
    });

    // Intercept JavaScript errors
    webContents.on('render-process-gone', (_event, details) => {
      logger.error('Renderer process crashed', {
        reason: details.reason,
        exitCode: details.exitCode,
        url: webContents.getURL(),
      });
    });

    // Note: 'crashed' event doesn't exist on webContents in newer Electron versions
    // Using 'render-process-gone' instead which is handled above

    // Intercept unresponsive events
    webContents.on('unresponsive', () => {
      logger.warn('Renderer process became unresponsive', {
        url: webContents.getURL(),
      });
    });

    webContents.on('responsive', () => {
      logger.info('Renderer process became responsive again', {
        url: webContents.getURL(),
      });
    });

    // Intercept navigation errors
    webContents.on('did-fail-load', (_event, errorCode, errorDescription, validatedURL) => {
      logger.error('Failed to load page', {
        errorCode,
        errorDescription,
        url: validatedURL,
      });
    });

    // Clean up when webContents is destroyed
    webContents.on('destroyed', () => {
      this.interceptedWebContents.delete(webContents);
    });

    logger.info('Console interception enabled for WebContents', {
      url: webContents.getURL(),
    });
  }

  /**
   * Map Electron console levels to our log levels
   * According to Electron docs:
   * 0: verbose (treat as info)
   * 1: info
   * 2: warning
   * 3: error
   */
  private mapConsoleLevel(level: number): 'debug' | 'info' | 'warn' | 'error' {
    switch (level) {
      case 0:
        return 'info'; // verbose (console.log)
      case 1:
        return 'info'; // info (console.info)
      case 2:
        return 'warn'; // warning (console.warn)
      case 3:
        return 'error'; // error (console.error)
      default:
        return 'info';
    }
  }

  /**
   * Enable verbose console message logging
   */
  enableVerboseLogging(webContents: WebContents): void {
    try {
      // Enable console API
      webContents.debugger.attach('1.3');
      void webContents.debugger.sendCommand('Runtime.enable');
      void webContents.debugger.sendCommand('Console.enable');

      webContents.debugger.on('message', (_event, method, params) => {
        if (method === 'Console.messageAdded') {
          const { level, text, source, line } = params.message;
          logger.logFromChromium(level as any, text, {
            source,
            line,
            url: webContents.getURL(),
            method: 'debugger',
          });
        } else if (method === 'Runtime.consoleAPICalled') {
          const { type, args, stackTrace } = params;
          const message = args
            .map((arg: any) => arg.value || arg.description || '[object]')
            .join(' ');
          logger.logFromChromium(type as any, message, {
            stackTrace,
            url: webContents.getURL(),
            method: 'runtime-api',
          });
        } else if (method === 'Runtime.exceptionThrown') {
          const { exceptionDetails } = params;
          logger.logFromChromium('error', exceptionDetails.text, {
            lineNumber: exceptionDetails.lineNumber,
            columnNumber: exceptionDetails.columnNumber,
            url: exceptionDetails.url || webContents.getURL(),
            method: 'exception',
          });
        }
      });

      logger.info('Verbose console logging enabled with debugger');
    } catch (error) {
      logger.warn('Failed to enable debugger-based console logging', error);
    }
  }

  /**
   * Inject console override script into renderer process
   */
  injectConsoleOverride(webContents: WebContents): void {
    const consoleOverrideScript = `
      (function() {
        // Wait for electronAPI to be available
        const waitForElectronAPI = () => {
          if (window.electronAPI && window.electronAPI.logToMain) {
            initializeConsoleOverride();
          } else {
            setTimeout(waitForElectronAPI, 100);
          }
        };

        const initializeConsoleOverride = () => {
          const originalConsole = {
            log: console.log.bind(console),
            info: console.info.bind(console),
            warn: console.warn.bind(console),
            error: console.error.bind(console),
            debug: console.debug.bind(console)
          };

          function createLogFunction(level) {
            return function(...args) {
              // Call original console method
              originalConsole[level](...args);

              // Send to main process via IPC
              try {
                const message = args.map(arg =>
                  typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');

                const stack = level === 'error' ? new Error().stack : undefined;

                const logLevel = level === 'log' ? 'info' : level;
                window.electronAPI.logToMain(logLevel, message, {
                  timestamp: new Date().toISOString(),
                  url: window.location.href,
                  userAgent: navigator.userAgent
                }, stack);
              } catch (error) {
                originalConsole.error('Failed to send log to main process:', error);
              }
            };
          }

          // Override console methods
          console.log = createLogFunction('log');
          console.info = createLogFunction('info');
          console.warn = createLogFunction('warn');
          console.error = createLogFunction('error');
          console.debug = createLogFunction('debug');

          // Capture unhandled errors
          window.addEventListener('error', (event) => {
            try {
              const errorMessage = 'Unhandled Error: ' + event.message;
              originalConsole.error(errorMessage, event);
              window.electronAPI.logToMain('error', errorMessage, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                url: window.location.href,
                type: 'javascript-error'
              }, event.error ? event.error.stack : undefined);
            } catch (error) {
              originalConsole.error('Failed to log unhandled error:', error);
            }
          });

          // Capture unhandled promise rejections
          window.addEventListener('unhandledrejection', (event) => {
            try {
              window.electronAPI.logToMain('error', 'Unhandled Promise Rejection: ' + event.reason, {
                url: window.location.href,
                reason: event.reason
              });
            } catch (error) {
              originalConsole.error('Failed to log unhandled promise rejection:', error);
            }
          });

          originalConsole.info('Console logging interceptor initialized');
        };

        waitForElectronAPI();
      })();
    `;

    // Inject the script after a short delay to ensure the page is ready
    setTimeout(() => {
      webContents.executeJavaScript(consoleOverrideScript).catch(error => {
        logger.error('Failed to inject console override script', error);
      });
    }, 100);
  }

  /**
   * Remove interception for all WebContents
   */
  cleanup(): void {
    this.interceptedWebContents.clear();
  }
}

// Create singleton instance
export const consoleInterceptor = new ConsoleInterceptor();
