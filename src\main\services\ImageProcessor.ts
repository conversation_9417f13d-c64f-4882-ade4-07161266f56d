import sharp from 'sharp';
import {
  DetectedFeature,
  EnhancementType,
  FeatureType,
  ImageData,
  ImageEnhancement,
  ImageProcessResult,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';

export interface ImageProcessingOptions {
  enhanceForOCR: boolean;
  upscaleRatio?: number;
  removeNoise: boolean;
  adjustContrast: boolean;
  adjustBrightness: boolean;
  sharpen: boolean;
  deskew: boolean;
  cropToContent: boolean;
  outputFormat?: 'png' | 'jpeg' | 'tiff' | 'webp';
  quality?: number;
  preserveMetadata: boolean;
}

export interface DeskewResult {
  angle: number;
  confidence: number;
  processedImage: Buffer;
}

export interface NoiseReductionOptions {
  method: 'gaussian' | 'median' | 'bilateral';
  strength: number; // 0-100
  preserveEdges: boolean;
}

export interface ContrastOptions {
  method: 'linear' | 'histogram' | 'adaptive';
  strength: number; // 0-200, 100 = no change
  gamma?: number;
}

export class ImageProcessor {
  private defaultOptions: ImageProcessingOptions = {
    enhanceForOCR: true,
    upscaleRatio: 2,
    removeNoise: true,
    adjustContrast: true,
    adjustBrightness: false,
    sharpen: true,
    deskew: true,
    cropToContent: false,
    outputFormat: 'png',
    quality: 95,
    preserveMetadata: false,
  };

  /**
   * Process image with specified enhancements
   */
  public async processImage(
    imageBuffer: Buffer,
    options: Partial<ImageProcessingOptions> = {}
  ): Promise<ImageProcessResult> {
    const startTime = Date.now();
    const processingOptions = { ...this.defaultOptions, ...options };

    try {
      logger.debug('Starting image processing', { options: processingOptions });

      // Get original image metadata
      const originalImage = await this.getImageData(imageBuffer);

      let processedBuffer = imageBuffer;
      const enhancements: ImageEnhancement[] = [];

      // Apply enhancements in optimal order
      if (processingOptions.deskew) {
        const deskewResult = this.deskewImage(processedBuffer);
        processedBuffer = deskewResult.processedImage;
        enhancements.push({
          type: EnhancementType.DESKEW,
          parameters: { angle: deskewResult.angle, confidence: deskewResult.confidence },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.cropToContent) {
        processedBuffer = await this.cropToContent(processedBuffer);
        enhancements.push({
          type: EnhancementType.CROP,
          parameters: { method: 'content-aware' },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.upscaleRatio && processingOptions.upscaleRatio > 1) {
        processedBuffer = await this.upscaleImage(processedBuffer, processingOptions.upscaleRatio);
        enhancements.push({
          type: EnhancementType.UPSCALE,
          parameters: { ratio: processingOptions.upscaleRatio },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.removeNoise) {
        processedBuffer = await this.removeNoise(processedBuffer);
        enhancements.push({
          type: EnhancementType.DENOISE,
          parameters: { method: 'gaussian', strength: 50 },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.adjustContrast) {
        processedBuffer = await this.adjustContrast(processedBuffer);
        enhancements.push({
          type: EnhancementType.CONTRAST,
          parameters: { method: 'adaptive', strength: 120 },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.adjustBrightness) {
        processedBuffer = await this.adjustBrightness(processedBuffer);
        enhancements.push({
          type: EnhancementType.BRIGHTNESS,
          parameters: { adjustment: 10 },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      if (processingOptions.sharpen) {
        processedBuffer = await this.sharpenImage(processedBuffer);
        enhancements.push({
          type: EnhancementType.SHARPEN,
          parameters: { sigma: 1, flat: 1, jagged: 2 },
          appliedAt: new Date(),
          processingTime: 0,
        });
      }

      // Convert to desired format
      if (processingOptions.outputFormat) {
        processedBuffer = await this.convertFormat(
          processedBuffer,
          processingOptions.outputFormat,
          processingOptions.quality
        );
      }

      const processedImage = await this.getImageData(processedBuffer);
      const detectedFeatures = await this.detectFeatures(processedBuffer);

      const processingTime = Date.now() - startTime;

      logger.debug('Image processing completed', {
        originalSize: `${originalImage.width}x${originalImage.height}`,
        processedSize: `${processedImage.width}x${processedImage.height}`,
        enhancements: enhancements.length,
        processingTime,
      });

      return {
        originalImage,
        processedImage,
        enhancements,
        detectedFeatures,
        processingTime,
        success: true,
      };
    } catch (error) {
      logger.error('Image processing failed', { error });

      const originalImage = await this.getImageData(imageBuffer);
      return {
        originalImage,
        processedImage: originalImage,
        enhancements: [],
        detectedFeatures: [],
        processingTime: Date.now() - startTime,
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Enhance image quality specifically for OCR
   */
  public async enhanceForOCR(imageBuffer: Buffer): Promise<Buffer> {
    try {
      return await sharp(imageBuffer)
        .resize({ width: 2000, withoutEnlargement: false }) // Upscale for better OCR
        .normalize() // Normalize contrast
        .sharpen({ sigma: 1, flat: 1, jagged: 2 }) // Enhance edges
        .png({ quality: 95, compressionLevel: 6 })
        .toBuffer();
    } catch (error) {
      logger.error('OCR enhancement failed', { error });
      throw new Error(`OCR enhancement failed: ${error.message}`);
    }
  }

  /**
   * Upscale image by specified ratio
   */
  public async upscaleImage(imageBuffer: Buffer, ratio: number): Promise<Buffer> {
    const metadata = await sharp(imageBuffer).metadata();
    const newWidth = Math.round((metadata.width || 0) * ratio);
    const newHeight = Math.round((metadata.height || 0) * ratio);

    return await sharp(imageBuffer)
      .resize(newWidth, newHeight, {
        kernel: sharp.kernel.lanczos3,
        withoutEnlargement: false,
      })
      .toBuffer();
  }

  /**
   * Remove noise from image
   */
  public async removeNoise(
    imageBuffer: Buffer,
    options: Partial<NoiseReductionOptions> = {}
  ): Promise<Buffer> {
    const { method = 'gaussian', strength = 50 } = options;

    switch (method) {
      case 'gaussian':
        return await sharp(imageBuffer)
          .blur(strength / 100) // Convert strength to blur sigma
          .toBuffer();

      case 'median':
        // Sharp doesn't have median filter, use gaussian as fallback
        return await sharp(imageBuffer).blur(0.5).toBuffer();

      default:
        return imageBuffer;
    }
  }

  /**
   * Adjust image contrast
   */
  public async adjustContrast(
    imageBuffer: Buffer,
    options: Partial<ContrastOptions> = {}
  ): Promise<Buffer> {
    const { method = 'adaptive', strength = 120, gamma = 1.0 } = options;

    switch (method) {
      case 'linear':
        return await sharp(imageBuffer)
          .linear(strength / 100, -(128 * (strength / 100 - 1)))
          .toBuffer();

      case 'histogram':
        return await sharp(imageBuffer).normalize().toBuffer();

      case 'adaptive':
        return await sharp(imageBuffer).normalize().gamma(gamma).toBuffer();

      default:
        return imageBuffer;
    }
  }

  /**
   * Adjust image brightness
   */
  public async adjustBrightness(imageBuffer: Buffer, adjustment: number = 10): Promise<Buffer> {
    return await sharp(imageBuffer)
      .modulate({ brightness: 1 + adjustment / 100 })
      .toBuffer();
  }

  /**
   * Sharpen image
   */
  public async sharpenImage(imageBuffer: Buffer): Promise<Buffer> {
    return await sharp(imageBuffer).sharpen({ sigma: 1, flat: 1, jagged: 2 }).toBuffer();
  }

  /**
   * Deskew image (correct rotation)
   */
  public deskewImage(imageBuffer: Buffer): DeskewResult {
    // Simplified deskew - in practice, would use more sophisticated algorithm
    // For now, just return the original image with 0 angle
    return {
      angle: 0,
      confidence: 1.0,
      processedImage: imageBuffer,
    };
  }

  /**
   * Crop image to content boundaries
   */
  public async cropToContent(imageBuffer: Buffer): Promise<Buffer> {
    return await sharp(imageBuffer)
      .trim({ threshold: 10 }) // Remove whitespace with 10% threshold
      .toBuffer();
  }

  /**
   * Convert image format
   */
  public async convertFormat(
    imageBuffer: Buffer,
    format: 'png' | 'jpeg' | 'tiff' | 'webp',
    quality: number = 95
  ): Promise<Buffer> {
    const sharpInstance = sharp(imageBuffer);

    switch (format) {
      case 'png':
        return await sharpInstance.png({ quality, compressionLevel: 6 }).toBuffer();
      case 'jpeg':
        return await sharpInstance.jpeg({ quality }).toBuffer();
      case 'tiff':
        return await sharpInstance.tiff({ quality }).toBuffer();
      case 'webp':
        return await sharpInstance.webp({ quality }).toBuffer();
      default:
        return imageBuffer;
    }
  }

  /**
   * Get image metadata and data
   */
  public async getImageData(imageBuffer: Buffer): Promise<ImageData> {
    const metadata = await sharp(imageBuffer).metadata();

    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || 'unknown',
      data: imageBuffer.buffer.slice(
        imageBuffer.byteOffset,
        imageBuffer.byteOffset + imageBuffer.byteLength
      ),
      dpi: metadata.density,
      colorSpace: metadata.space,
    };
  }

  /**
   * Detect features in image (simplified implementation)
   */
  public async detectFeatures(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    const features: DetectedFeature[] = [];

    try {
      const metadata = await sharp(imageBuffer).metadata();

      // Simplified feature detection - would use computer vision algorithms in practice
      // For now, just detect if image contains text-like regions based on contrast
      const stats = await sharp(imageBuffer).greyscale().stats();

      if (stats.channels && stats.channels[0]) {
        const channel = stats.channels[0];
        const contrast = channel.max - channel.min;

        if (contrast > 100) {
          // High contrast suggests text
          features.push({
            type: FeatureType.TEXT_BLOCK,
            bounds: {
              x: 0,
              y: 0,
              width: metadata.width || 0,
              height: metadata.height || 0,
              pageNumber: 1,
            },
            confidence: Math.min(contrast / 255, 1.0),
            properties: {
              contrast,
              mean: channel.mean,
              std: channel.std,
            },
          });
        }
      }
    } catch (error) {
      logger.warn('Feature detection failed', { error });
    }

    return features;
  }

  /**
   * Batch process multiple images
   */
  public async batchProcess(
    images: Buffer[],
    options: Partial<ImageProcessingOptions> = {}
  ): Promise<ImageProcessResult[]> {
    const results: ImageProcessResult[] = [];

    for (let i = 0; i < images.length; i++) {
      logger.debug(`Processing image ${i + 1}/${images.length}`);
      const result = await this.processImage(images[i], options);
      results.push(result);
    }

    return results;
  }

  /**
   * Get optimal processing options for OCR
   */
  public getOCROptimizedOptions(): ImageProcessingOptions {
    return {
      enhanceForOCR: true,
      upscaleRatio: 2,
      removeNoise: true,
      adjustContrast: true,
      adjustBrightness: false,
      sharpen: true,
      deskew: true,
      cropToContent: true,
      outputFormat: 'png',
      quality: 95,
      preserveMetadata: false,
    };
  }
}
