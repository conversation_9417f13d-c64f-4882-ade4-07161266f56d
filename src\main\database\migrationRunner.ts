import fs from 'fs-extra';
import knex, { Knex } from 'knex';
import path from 'path';
import { logger } from '../utils/logger';

export interface MigrationInfo {
  name: string;
  batch: number;
  migration_time: Date;
}

export interface MigrationResult {
  success: boolean;
  migrationsRun: string[];
  error?: Error;
}

export interface RollbackResult {
  success: boolean;
  migrationsRolledBack: string[];
  error?: Error;
}

class MigrationRunner {
  private static instance: MigrationRunner;
  private knexInstance: Knex | null = null;
  private config: Knex.Config;

  private constructor() {
    const environment = process.env.NODE_ENV || 'development';
    const knexConfig = require('../../../knexfile.js');
    this.config = knexConfig[environment];
  }

  public static getInstance(): MigrationRunner {
    if (!MigrationRunner.instance) {
      MigrationRunner.instance = new MigrationRunner();
    }
    return MigrationRunner.instance;
  }

  private async initializeKnex(): Promise<void> {
    if (this.knexInstance) {
      return;
    }

    try {
      // Ensure database directory exists
      const dbPath = this.config.connection.filename;
      if (dbPath !== ':memory:') {
        await fs.ensureDir(path.dirname(dbPath));
      }

      // Ensure migrations directory exists
      const migrationsDir = path.resolve(this.config.migrations.directory);
      await fs.ensureDir(migrationsDir);

      this.knexInstance = knex(this.config);

      logger.info('Knex migration runner initialized', {
        environment: process.env.NODE_ENV || 'development',
        database: dbPath,
        migrationsDir
      });
    } catch (error) {
      logger.error('Failed to initialize Knex migration runner', { error });
      throw error;
    }
  }

  public async runMigrations(): Promise<MigrationResult> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      logger.info('Starting database migrations');

      // Create migration tracking table if it doesn't exist
      await this.ensureMigrationTable();

      // Run pending migrations
      const [batchNo, migrationsRun] = await this.knexInstance.migrate.latest();

      if (migrationsRun.length > 0) {
        logger.info('Migrations completed successfully', {
          batch: batchNo,
          migrations: migrationsRun
        });
      } else {
        logger.info('No pending migrations found');
      }

      return {
        success: true,
        migrationsRun
      };
    } catch (error) {
      logger.error('Migration failed', { error });
      return {
        success: false,
        migrationsRun: [],
        error: error as Error
      };
    }
  }

  public async rollbackMigrations(steps?: number): Promise<RollbackResult> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      logger.info('Starting migration rollback', { steps });

      let migrationsRolledBack: string[];

      if (steps) {
        // Rollback specific number of batches
        [, migrationsRolledBack] = await this.knexInstance.migrate.rollback({}, false);

        // Continue rolling back for the specified number of steps
        for (let i = 1; i < steps; i++) {
          const [, additionalRollbacks] = await this.knexInstance.migrate.rollback({}, false);
          migrationsRolledBack.push(...additionalRollbacks);

          if (additionalRollbacks.length === 0) {
            break; // No more migrations to rollback
          }
        }
      } else {
        // Rollback last batch
        [, migrationsRolledBack] = await this.knexInstance.migrate.rollback();
      }

      if (migrationsRolledBack.length > 0) {
        logger.info('Rollback completed successfully', {
          migrations: migrationsRolledBack
        });
      } else {
        logger.info('No migrations to rollback');
      }

      return {
        success: true,
        migrationsRolledBack
      };
    } catch (error) {
      logger.error('Migration rollback failed', { error });
      return {
        success: false,
        migrationsRolledBack: [],
        error: error as Error
      };
    }
  }

  public async getMigrationStatus(): Promise<MigrationInfo[]> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      // Get completed migrations
      const completedMigrations = await this.knexInstance('knex_migrations')
        .select('name', 'batch', 'migration_time')
        .orderBy('id');

      return completedMigrations.map(migration => ({
        name: migration.name,
        batch: migration.batch,
        migration_time: new Date(migration.migration_time)
      }));
    } catch (error) {
      logger.error('Failed to get migration status', { error });
      throw error;
    }
  }

  public async getPendingMigrations(): Promise<string[]> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      // Get all migration files
      const migrationsDir = path.resolve(this.config.migrations.directory);
      const migrationFiles = await fs.readdir(migrationsDir);
      const allMigrations = migrationFiles
        .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
        .filter(file => !file.startsWith('.'))
        .sort();

      // Get completed migrations
      const completedMigrations = await this.getMigrationStatus();
      const completedNames = completedMigrations.map(m => m.name);

      // Find pending migrations
      const pendingMigrations = allMigrations.filter(migration => {
        const migrationName = migration.replace(/\.(ts|js)$/, '');
        return !completedNames.includes(migrationName);
      });

      return pendingMigrations;
    } catch (error) {
      logger.error('Failed to get pending migrations', { error });
      throw error;
    }
  }

  public async validateMigrations(): Promise<boolean> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      // Check if migration table exists and is accessible
      const tableExists = await this.knexInstance.schema.hasTable('knex_migrations');
      if (!tableExists) {
        logger.warn('Migration table does not exist');
        return false;
      }

      // Validate migration files exist
      const migrationsDir = path.resolve(this.config.migrations.directory);
      const dirExists = await fs.pathExists(migrationsDir);
      if (!dirExists) {
        logger.warn('Migrations directory does not exist', { dir: migrationsDir });
        return false;
      }

      // Check for migration file consistency
      const completedMigrations = await this.getMigrationStatus();
      for (const migration of completedMigrations) {
        const migrationFile = path.join(migrationsDir, `${migration.name}.ts`);
        const fileExists = await fs.pathExists(migrationFile);
        if (!fileExists) {
          logger.warn('Migration file missing for completed migration', {
            migration: migration.name,
            file: migrationFile
          });
          return false;
        }
      }

      logger.info('Migration validation passed');
      return true;
    } catch (error) {
      logger.error('Migration validation failed', { error });
      return false;
    }
  }

  private async ensureMigrationTable(): Promise<void> {
    if (!this.knexInstance) {
      throw new Error('Knex instance not initialized');
    }

    try {
      // Check if migration table exists
      const tableExists = await this.knexInstance.schema.hasTable('knex_migrations');

      if (!tableExists) {
        logger.info('Creating migration tracking table');

        await this.knexInstance.schema.createTable('knex_migrations', (table) => {
          table.increments('id').primary();
          table.string('name').notNullable();
          table.integer('batch').notNullable();
          table.timestamp('migration_time').defaultTo(this.knexInstance!.fn.now());

          table.unique(['name']);
          table.index(['batch']);
        });

        // Also create migration lock table
        const lockTableExists = await this.knexInstance.schema.hasTable('knex_migrations_lock');
        if (!lockTableExists) {
          await this.knexInstance.schema.createTable('knex_migrations_lock', (table) => {
            table.increments('index').primary();
            table.integer('is_locked').defaultTo(0);
          });

          // Insert initial lock row
          await this.knexInstance('knex_migrations_lock').insert({ is_locked: 0 });
        }
      }
    } catch (error) {
      logger.error('Failed to ensure migration table exists', { error });
      throw error;
    }
  }

  public async runMigrationsOnStartup(): Promise<void> {
    try {
      logger.info('Running startup migrations check');

      // Validate existing migrations
      const isValid = await this.validateMigrations();
      if (!isValid) {
        throw new Error('Migration validation failed');
      }

      // Check for pending migrations
      const pendingMigrations = await this.getPendingMigrations();

      if (pendingMigrations.length > 0) {
        logger.info('Found pending migrations, running automatically', {
          pending: pendingMigrations
        });

        const result = await this.runMigrations();
        if (!result.success) {
          throw result.error || new Error('Migration failed');
        }
      } else {
        logger.info('No pending migrations found');
      }
    } catch (error) {
      logger.error('Startup migration check failed', { error });
      throw error;
    }
  }

  public async createMigration(name: string): Promise<string> {
    try {
      await this.initializeKnex();

      if (!this.knexInstance) {
        throw new Error('Knex instance not initialized');
      }

      // Generate timestamp
      const timestamp = new Date().toISOString()
        .replace(/[-:]/g, '')
        .replace(/\..+/, '')
        .replace('T', '');

      const migrationName = `${timestamp}_${name}`;
      const migrationPath = await this.knexInstance.migrate.make(migrationName);

      logger.info('Migration file created', {
        name: migrationName,
        path: migrationPath
      });

      return migrationPath;
    } catch (error) {
      logger.error('Failed to create migration', { error, name });
      throw error;
    }
  }

  public async close(): Promise<void> {
    if (this.knexInstance) {
      try {
        await this.knexInstance.destroy();
        this.knexInstance = null;
        logger.info('Migration runner closed');
      } catch (error) {
        logger.error('Error closing migration runner', { error });
        throw error;
      }
    }
  }

  // Utility method to get current schema version
  public async getCurrentSchemaVersion(): Promise<number> {
    try {
      const migrations = await this.getMigrationStatus();
      return migrations.length > 0 ? Math.max(...migrations.map(m => m.batch)) : 0;
    } catch (error) {
      logger.error('Failed to get current schema version', { error });
      return 0;
    }
  }

  // Method to check if database needs migration
  public async needsMigration(): Promise<boolean> {
    try {
      const pendingMigrations = await this.getPendingMigrations();
      return pendingMigrations.length > 0;
    } catch (error) {
      logger.error('Failed to check if migration is needed', { error });
      return false;
    }
  }
}

// Export singleton instance
export const migrationRunner = MigrationRunner.getInstance();

// Export types
export type { MigrationRunner };
