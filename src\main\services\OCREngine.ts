import NodeCache from 'node-cache';
import { create<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Worker } from 'tesseract.js';

interface TesseractBBox {
  x0: number;
  y0: number;
  x1: number;
  y1: number;
}

interface TesseractWord {
  text: string;
  confidence: number;
  bbox: TesseractBBox;
  font_size: number;
  is_bold: boolean;
  is_italic: boolean;
}

interface TesseractLine {
  text: string;
  confidence: number;
  bbox: TesseractBBox;
  words: TesseractWord[];
  baseline: number;
}

interface TesseractParagraph {
  text: string;
  confidence: number;
  bbox: TesseractBBox;
  lines: TesseractLine[];
}

import {
  DocumentCoordinates,
  FormField,
  FormFieldType,
  OCRLine,
  OCROptions,
  OCRParagraph,
  OCRResult,
  OCRWord,
  PageSegmentationMode,
  TableCell,
  TableRow,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';

export interface OCREngineConfig {
  maxWorkers: number;
  cacheEnabled: boolean;
  cacheTTL: number; // seconds
  defaultLanguage: string;
  supportedLanguages: string[];
  workerOptions: {
    logger: (message: string) => void;
    errorHandler: (error: Error) => void;
  };
}

export interface TableData {
  rows: TableRow[];
  headers?: string[];
  confidence: number;
  bounds: DocumentCoordinates;
}

export class OCREngine {
  private workers: Worker[] = [];
  private availableWorkers: Worker[] = [];
  private readonly busyWorkers: Set<Worker> = new Set();
  private readonly cache: NodeCache;
  private readonly config: OCREngineConfig;
  private isInitialized = false;

  constructor(config: Partial<OCREngineConfig> = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || 3,
      cacheEnabled: config.cacheEnabled ?? true,
      cacheTTL: config.cacheTTL || 3600, // 1 hour
      defaultLanguage: config.defaultLanguage || 'eng',
      supportedLanguages: config.supportedLanguages || ['eng', 'spa', 'fra', 'deu'],
      workerOptions: config.workerOptions || {
        logger: (message: string) => logger.debug('Tesseract:', message),
        errorHandler: (error: Error) => logger.error('Tesseract error:', error),
      },
    };

    this.cache = new NodeCache({
      stdTTL: this.config.cacheEnabled ? this.config.cacheTTL : 0,
      checkperiod: this.config.cacheEnabled ? this.config.cacheTTL * 0.2 : 0,
      useClones: false,
    });
  }

  /**
   * Initialize the OCR engine with worker pool
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing OCR engine with worker pool', {
        maxWorkers: this.config.maxWorkers,
        languages: this.config.supportedLanguages,
      });

      // Create worker pool
      for (let i = 0; i < this.config.maxWorkers; i++) {
        const worker = await this.createWorker();
        this.workers.push(worker);
        this.availableWorkers.push(worker);
      }

      this.isInitialized = true;
      logger.info('OCR engine initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize OCR engine', { error });
      throw new Error(`OCR engine initialization failed: ${error.message}`);
    }
  }

  /**
   * Create and configure a Tesseract worker
   */
  private async createWorker(): Promise<Worker> {
    const worker = await createWorker({
      logger: this.config.workerOptions.logger,
      errorHandler: this.config.workerOptions.errorHandler,
    });

    // Load language data for all supported languages
    await worker.loadLanguage(this.config.supportedLanguages.join('+'));
    await worker.initialize(this.config.supportedLanguages.join('+'));

    // Set default parameters
    await worker.setParameters({
      tessedit_pageseg_mode: PSM.AUTO,
      tessedit_ocr_engine_mode: OEM.LSTM_ONLY,
      tessedit_char_whitelist: '',
      tessedit_char_blacklist: '',
      preserve_interword_spaces: '1',
    });

    return worker;
  }

  /**
   * Get an available worker from the pool
   */
  private async getWorker(): Promise<Worker> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    // Wait for an available worker
    while (this.availableWorkers.length === 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const worker = this.availableWorkers.pop();
    if (!worker) {
      throw new Error('No available OCR workers');
    }
    this.busyWorkers.add(worker);
    return worker;
  }

  /**
   * Return a worker to the available pool
   */
  private releaseWorker(worker: Worker): void {
    this.busyWorkers.delete(worker);
    this.availableWorkers.push(worker);
  }

  /**
   * Extract text from image buffer
   */
  public async extractTextFromImage(
    image: Buffer,
    options: Partial<OCROptions> = {}
  ): Promise<OCRResult> {
    const startTime = Date.now();

    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(image, options);
      if (this.config.cacheEnabled && this.cache.has(cacheKey)) {
        logger.debug('OCR result found in cache');
        return this.cache.get(cacheKey) as OCRResult;
      }

      const worker = await this.getWorker();

      try {
        // Configure worker for this specific request
        await this.configureWorker(worker, options);

        // Perform OCR
        const result = await worker.recognize(image);

        // Convert Tesseract result to our OCR result format
        const ocrResult = this.convertTesseractResult(result, startTime);

        // Cache the result
        if (this.config.cacheEnabled) {
          this.cache.set(cacheKey, ocrResult);
        }

        logger.debug('OCR completed successfully', {
          confidence: ocrResult.confidence,
          processingTime: ocrResult.processingTime,
          textLength: ocrResult.text.length,
        });

        return ocrResult;
      } finally {
        this.releaseWorker(worker);
      }
    } catch (error) {
      logger.error('OCR extraction failed', { error });
      throw new Error(`OCR extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF buffer (converts to images first)
   */
  public extractTextFromPDF(_pdf: Buffer): Promise<OCRResult> {
    // This method would typically work with the PDFProcessor
    // to convert PDF pages to images first, then perform OCR
    return Promise.reject(new Error('PDF OCR extraction requires integration with PDFProcessor'));
  }

  /**
   * Get confidence score from OCR result
   */
  public getConfidenceScore(result: OCRResult): number {
    return result.confidence;
  }

  /**
   * Detect language in image
   */
  public async detectLanguage(image: Buffer): Promise<string> {
    const worker = await this.getWorker();

    try {
      await worker.setParameters({
        tessedit_pageseg_mode: PSM.AUTO_OSD,
      });

      const result = await worker.recognize(image);
      return result.data.text ? this.config.defaultLanguage : 'unknown';
    } finally {
      this.releaseWorker(worker);
    }
  }

  /**
   * Extract tabular data from image
   */
  public async extractTabularData(image: Buffer): Promise<TableData> {
    const ocrResult = await this.extractTextFromImage(image, {
      pageSegmentationMode: PageSegmentationMode.SINGLE_BLOCK,
      preserveInterwordSpaces: true,
    });

    // Analyze OCR result to detect table structure
    return this.analyzeTableStructure(ocrResult);
  }

  /**
   * Extract form fields from image
   */
  public async extractFormFields(image: Buffer): Promise<FormField[]> {
    const ocrResult = await this.extractTextFromImage(image, {
      pageSegmentationMode: PageSegmentationMode.SPARSE_TEXT,
    });

    // Analyze OCR result to detect form fields
    return this.detectFormFields(ocrResult);
  }

  /**
   * Configure worker with specific options
   */
  private async configureWorker(worker: Worker, options: Partial<OCROptions>): Promise<void> {
    const params: Record<string, string> = {};

    if (options.pageSegmentationMode !== undefined) {
      params.tessedit_pageseg_mode = options.pageSegmentationMode.toString();
    }

    if (options.ocrEngineMode !== undefined) {
      params.tessedit_ocr_engine_mode = options.ocrEngineMode.toString();
    }

    if (options.whitelist) {
      params.tessedit_char_whitelist = options.whitelist;
    }

    if (options.blacklist) {
      params.tessedit_char_blacklist = options.blacklist;
    }

    if (options.preserveInterwordSpaces !== undefined) {
      params.preserve_interword_spaces = options.preserveInterwordSpaces ? '1' : '0';
    }

    if (Object.keys(params).length > 0) {
      await worker.setParameters(params);
    }
  }

  /**
   * Convert Tesseract result to our OCR result format
   */
  private convertTesseractResult(tesseractResult: any, startTime: number): OCRResult {
    const processingTime = Date.now() - startTime;

    const words: OCRWord[] = tesseractResult.data.words.map((word: TesseractWord) => ({
      text: word.text,
      confidence: word.confidence,
      bounds: {
        x: word.bbox.x0,
        y: word.bbox.y0,
        width: word.bbox.x1 - word.bbox.x0,
        height: word.bbox.y1 - word.bbox.y0,
        pageNumber: 1,
      },
      fontSize: word.font_size,
      isBold: word.is_bold,
      isItalic: word.is_italic,
    }));

    const lines: OCRLine[] = tesseractResult.data.lines.map((line: TesseractLine) => ({
      text: line.text,
      confidence: line.confidence,
      bounds: {
        x: line.bbox.x0,
        y: line.bbox.y0,
        width: line.bbox.x1 - line.bbox.x0,
        height: line.bbox.y1 - line.bbox.y0,
        pageNumber: 1,
      },
      words: line.words.map((word: TesseractWord) =>
        words.findIndex(w => w.text === word.text && w.bounds.x === word.bbox.x0)
      ),
      baseline: line.baseline,
    }));

    const paragraphs: OCRParagraph[] = tesseractResult.data.paragraphs.map(
      (para: TesseractParagraph) => ({
        text: para.text,
        confidence: para.confidence,
        bounds: {
          x: para.bbox.x0,
          y: para.bbox.y0,
          width: para.bbox.x1 - para.bbox.x0,
          height: para.bbox.y1 - para.bbox.y0,
          pageNumber: 1,
        },
        lines: para.lines.map((line: TesseractLine) =>
          lines.findIndex(l => l.text === line.text && l.bounds.x === line.bbox.x0)
        ),
      })
    );

    return {
      text: tesseractResult.data.text,
      confidence: tesseractResult.data.confidence,
      words,
      lines,
      paragraphs,
      language: this.config.defaultLanguage,
      processingTime,
      imageEnhancements: [],
    };
  }

  /**
   * Generate cache key for OCR result
   */
  private generateCacheKey(image: Buffer, options: Partial<OCROptions>): string {
    const crypto = require('crypto');
    const hash = crypto.createHash('md5');
    hash.update(image);
    hash.update(JSON.stringify(options));
    return hash.digest('hex');
  }

  /**
   * Analyze OCR result to detect table structure
   */
  private analyzeTableStructure(ocrResult: OCRResult): TableData {
    // Simplified table detection - would need more sophisticated algorithm
    const rows: TableRow[] = [];

    // Group lines that are horizontally aligned
    const groupedLines = this.groupLinesByVerticalPosition(ocrResult.lines);

    for (const lineGroup of groupedLines) {
      const cells: TableCell[] = lineGroup.map(line => ({
        value: line.text.trim(),
        type: this.detectCellType(line.text),
        bounds: line.bounds,
      }));

      if (cells.length > 1) {
        // Only consider rows with multiple cells
        rows.push({ cells });
      }
    }

    return {
      rows,
      confidence: ocrResult.confidence,
      bounds: this.calculateTableBounds(rows),
    };
  }

  /**
   * Group lines by vertical position to detect table rows
   */
  private groupLinesByVerticalPosition(lines: OCRLine[]): OCRLine[][] {
    const groups: OCRLine[][] = [];
    const tolerance = 10; // pixels

    for (const line of lines) {
      let addedToGroup = false;

      for (const group of groups) {
        const avgY = group.reduce((sum, l) => sum + l.bounds.y, 0) / group.length;
        if (Math.abs(line.bounds.y - avgY) <= tolerance) {
          group.push(line);
          addedToGroup = true;
          break;
        }
      }

      if (!addedToGroup) {
        groups.push([line]);
      }
    }

    return groups.sort((a, b) => (a[0]?.bounds.y || 0) - (b[0]?.bounds.y || 0));
  }

  /**
   * Detect cell data type
   */
  private detectCellType(text: string): 'text' | 'number' | 'date' | 'boolean' {
    const trimmed = text.trim();

    if (/^\d+\.?\d*$/.test(trimmed)) return 'number';
    if (/^\d{1,2}\/\d{1,2}\/\d{2,4}$/.test(trimmed)) return 'date';
    if (/^(true|false|yes|no)$/i.test(trimmed)) return 'boolean';

    return 'text';
  }

  /**
   * Calculate table bounds from rows
   */
  private calculateTableBounds(rows: TableRow[]): DocumentCoordinates {
    if (rows.length === 0) {
      return { x: 0, y: 0, width: 0, height: 0, pageNumber: 1 };
    }

    let minX = Infinity,
      minY = Infinity,
      maxX = 0,
      maxY = 0;

    for (const row of rows) {
      for (const cell of row.cells) {
        minX = Math.min(minX, cell.bounds.x);
        minY = Math.min(minY, cell.bounds.y);
        maxX = Math.max(maxX, cell.bounds.x + cell.bounds.width);
        maxY = Math.max(maxY, cell.bounds.y + cell.bounds.height);
      }
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
      pageNumber: 1,
    };
  }

  /**
   * Detect form fields from OCR result
   */
  private detectFormFields(ocrResult: OCRResult): FormField[] {
    const formFields: FormField[] = [];

    // Look for patterns that indicate form fields
    for (const line of ocrResult.lines) {
      const text = line.text.trim();

      // Detect checkbox patterns
      if (/\[\s*\]|☐|☑/.test(text)) {
        formFields.push({
          id: `checkbox_${formFields.length}`,
          name: text.replace(/\[\s*\]|☐|☑/, '').trim(),
          type: FormFieldType.CHECKBOX,
          value: /☑/.test(text),
          required: false,
          readonly: false,
          bounds: line.bounds,
        });
      }

      // Detect text input patterns (lines with underscores)
      if (/_+/.test(text)) {
        formFields.push({
          id: `text_${formFields.length}`,
          name: text.replace(/_+/g, '').trim(),
          type: FormFieldType.TEXT,
          value: '',
          required: false,
          readonly: false,
          bounds: line.bounds,
        });
      }
    }

    return formFields;
  }

  /**
   * Extract handwriting and signatures from image
   */
  public async extractHandwriting(image: Buffer): Promise<{
    handwritingRegions: DocumentCoordinates[];
    signatureRegions: DocumentCoordinates[];
    confidence: number;
  }> {
    const ocrResult = await this.extractTextFromImage(image, {
      pageSegmentationMode: PageSegmentationMode.SPARSE_TEXT,
      minimumConfidence: 30, // Lower threshold for handwriting
    });

    const handwritingRegions: DocumentCoordinates[] = [];
    const signatureRegions: DocumentCoordinates[] = [];

    // Analyze words for handwriting characteristics
    for (const word of ocrResult.words) {
      if (word.confidence < 60) {
        // Low confidence often indicates handwriting
        // Check if it looks like a signature (short, low confidence, specific patterns)
        if (this.isLikelySignature(word)) {
          signatureRegions.push(word.bounds);
        } else {
          handwritingRegions.push(word.bounds);
        }
      }
    }

    return {
      handwritingRegions,
      signatureRegions,
      confidence: ocrResult.confidence,
    };
  }

  /**
   * Detect and decode barcodes and QR codes
   */
  public detectBarcodes(_image: Buffer): Promise<{
    barcodes: Array<{
      type: 'qr' | 'barcode';
      data: string;
      bounds: DocumentCoordinates;
      confidence: number;
    }>;
  }> {
    // Simplified barcode detection - would use specialized library like jsQR or QuaggaJS
    // For now, return empty array as placeholder
    return Promise.resolve({ barcodes: [] });
  }

  /**
   * Create coordinate mapping between original image and extracted text
   */
  public async createCoordinateMapping(
    originalImage: Buffer,
    extractedText: OCRResult
  ): Promise<
    Array<{
      textSegment: string;
      originalCoordinates: DocumentCoordinates;
      normalizedCoordinates: DocumentCoordinates;
    }>
  > {
    const mappings: Array<{
      textSegment: string;
      originalCoordinates: DocumentCoordinates;
      normalizedCoordinates: DocumentCoordinates;
    }> = [];

    // Get original image dimensions
    const sharp = require('sharp');
    const metadata = await sharp(originalImage).metadata();
    const originalWidth = metadata.width || 1;
    const originalHeight = metadata.height || 1;

    // Create mappings for each word
    for (const word of extractedText.words) {
      mappings.push({
        textSegment: word.text,
        originalCoordinates: word.bounds,
        normalizedCoordinates: {
          x: word.bounds.x / originalWidth,
          y: word.bounds.y / originalHeight,
          width: word.bounds.width / originalWidth,
          height: word.bounds.height / originalHeight,
          pageNumber: word.bounds.pageNumber,
        },
      });
    }

    return mappings;
  }

  /**
   * Advanced table detection with cell structure analysis
   */
  public async detectAdvancedTables(image: Buffer): Promise<{
    tables: Array<{
      bounds: DocumentCoordinates;
      rows: number;
      columns: number;
      cells: Array<{
        row: number;
        column: number;
        text: string;
        bounds: DocumentCoordinates;
        confidence: number;
      }>;
      confidence: number;
    }>;
  }> {
    const ocrResult = await this.extractTextFromImage(image, {
      pageSegmentationMode: PageSegmentationMode.SINGLE_BLOCK,
    });

    const tables: Array<{
      bounds: DocumentCoordinates;
      rows: number;
      columns: number;
      cells: Array<{
        row: number;
        column: number;
        text: string;
        bounds: DocumentCoordinates;
        confidence: number;
      }>;
      confidence: number;
    }> = [];

    // Analyze spatial relationships to detect table structure
    const gridAnalysis = this.analyzeGridStructure(ocrResult.words);

    if (gridAnalysis.isTable) {
      tables.push({
        bounds: gridAnalysis.bounds,
        rows: gridAnalysis.rows,
        columns: gridAnalysis.columns,
        cells: gridAnalysis.cells,
        confidence: gridAnalysis.confidence,
      });
    }

    return { tables };
  }

  /**
   * Analyze grid structure from OCR words
   */
  private analyzeGridStructure(words: OCRWord[]): {
    isTable: boolean;
    bounds: DocumentCoordinates;
    rows: number;
    columns: number;
    cells: Array<{
      row: number;
      column: number;
      text: string;
      bounds: DocumentCoordinates;
      confidence: number;
    }>;
    confidence: number;
  } {
    if (words.length < 4) {
      return {
        isTable: false,
        bounds: { x: 0, y: 0, width: 0, height: 0, pageNumber: 1 },
        rows: 0,
        columns: 0,
        cells: [],
        confidence: 0,
      };
    }

    // Group words by approximate Y position (rows)
    const rowGroups = this.groupWordsByRows(words);

    // Check if we have consistent column structure
    const columnAnalysis = this.analyzeColumnStructure(rowGroups);

    if (columnAnalysis.isConsistent && rowGroups.length >= 2) {
      const cells = this.createCellStructure(rowGroups, columnAnalysis.columnPositions);

      return {
        isTable: true,
        bounds: this.calculateBounds(words),
        rows: rowGroups.length,
        columns: columnAnalysis.columnPositions.length,
        cells,
        confidence: columnAnalysis.confidence,
      };
    }

    return {
      isTable: false,
      bounds: { x: 0, y: 0, width: 0, height: 0, pageNumber: 1 },
      rows: 0,
      columns: 0,
      cells: [],
      confidence: 0,
    };
  }

  /**
   * Group words by rows based on Y position
   */
  private groupWordsByRows(words: OCRWord[]): OCRWord[][] {
    const tolerance = 10; // pixels
    const groups: OCRWord[][] = [];

    const sortedWords = [...words].sort((a, b) => a.bounds.y - b.bounds.y);

    for (const word of sortedWords) {
      let addedToGroup = false;

      for (const group of groups) {
        const avgY = group.reduce((sum, w) => sum + w.bounds.y, 0) / group.length;
        if (Math.abs(word.bounds.y - avgY) <= tolerance) {
          group.push(word);
          addedToGroup = true;
          break;
        }
      }

      if (!addedToGroup) {
        groups.push([word]);
      }
    }

    // Sort words within each group by X position
    groups.forEach(group => {
      group.sort((a, b) => a.bounds.x - b.bounds.x);
    });

    return groups;
  }

  /**
   * Analyze column structure consistency
   */
  private analyzeColumnStructure(rowGroups: OCRWord[][]): {
    isConsistent: boolean;
    columnPositions: number[];
    confidence: number;
  } {
    if (rowGroups.length < 2) {
      return { isConsistent: false, columnPositions: [], confidence: 0 };
    }

    // Get X positions from all rows
    const allXPositions: number[] = [];
    rowGroups.forEach(row => {
      row.forEach(word => allXPositions.push(word.bounds.x));
    });

    // Cluster X positions to find column boundaries
    const columnPositions = this.clusterPositions(allXPositions, 20); // 20px tolerance

    // Check consistency across rows
    let consistentRows = 0;
    for (const row of rowGroups) {
      const rowPositions = row.map(word => word.bounds.x);
      const matchedColumns = this.matchPositionsToColumns(rowPositions, columnPositions, 20);

      if (matchedColumns >= Math.min(columnPositions.length * 0.7, row.length)) {
        consistentRows++;
      }
    }

    const consistency = consistentRows / rowGroups.length;

    return {
      isConsistent: consistency >= 0.7,
      columnPositions,
      confidence: consistency,
    };
  }

  /**
   * Cluster positions to find column boundaries
   */
  private clusterPositions(positions: number[], tolerance: number): number[] {
    const sorted = [...positions].sort((a, b) => a - b);
    const clusters: number[] = [];

    let currentCluster = [sorted[0]];

    for (let i = 1; i < sorted.length; i++) {
      if ((sorted[i] || 0) - (sorted[i - 1] || 0) <= tolerance) {
        currentCluster.push(sorted[i]);
      } else {
        // Calculate cluster center
        const center = currentCluster.reduce((sum, pos) => sum + pos, 0) / currentCluster.length;
        clusters.push(center);
        currentCluster = [sorted[i]];
      }
    }

    // Add last cluster
    if (currentCluster.length > 0) {
      const center = currentCluster.reduce((sum, pos) => sum + pos, 0) / currentCluster.length;
      clusters.push(center);
    }

    return clusters;
  }

  /**
   * Match row positions to column positions
   */
  private matchPositionsToColumns(
    rowPositions: number[],
    columnPositions: number[],
    tolerance: number
  ): number {
    let matches = 0;

    for (const rowPos of rowPositions) {
      for (const colPos of columnPositions) {
        if (Math.abs(rowPos - colPos) <= tolerance) {
          matches++;
          break;
        }
      }
    }

    return matches;
  }

  /**
   * Create cell structure from row groups and column positions
   */
  private createCellStructure(
    rowGroups: OCRWord[][],
    columnPositions: number[]
  ): Array<{
    row: number;
    column: number;
    text: string;
    bounds: DocumentCoordinates;
    confidence: number;
  }> {
    const cells: Array<{
      row: number;
      column: number;
      text: string;
      bounds: DocumentCoordinates;
      confidence: number;
    }> = [];

    rowGroups.forEach((row, rowIndex) => {
      row.forEach(word => {
        // Find closest column
        let closestColumn = 0;
        let minDistance = Math.abs(word.bounds.x - columnPositions[0]);

        for (let i = 1; i < columnPositions.length; i++) {
          const distance = Math.abs(word.bounds.x - columnPositions[i]);
          if (distance < minDistance) {
            minDistance = distance;
            closestColumn = i;
          }
        }

        cells.push({
          row: rowIndex,
          column: closestColumn,
          text: word.text,
          bounds: word.bounds,
          confidence: word.confidence,
        });
      });
    });

    return cells;
  }

  /**
   * Calculate bounds from words
   */
  private calculateBounds(words: OCRWord[]): DocumentCoordinates {
    if (words.length === 0) {
      return { x: 0, y: 0, width: 0, height: 0, pageNumber: 1 };
    }

    let minX = Infinity,
      minY = Infinity,
      maxX = 0,
      maxY = 0;

    for (const word of words) {
      minX = Math.min(minX, word.bounds.x);
      minY = Math.min(minY, word.bounds.y);
      maxX = Math.max(maxX, word.bounds.x + word.bounds.width);
      maxY = Math.max(maxY, word.bounds.y + word.bounds.height);
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
      pageNumber: words[0].bounds.pageNumber,
    };
  }

  /**
   * Check if a word is likely a signature
   */
  private isLikelySignature(word: OCRWord): boolean {
    // Signatures typically have:
    // - Low OCR confidence
    // - Cursive/connected characters
    // - Specific aspect ratios
    // - Limited character set

    const aspectRatio = word.bounds.width / word.bounds.height;
    const hasLowConfidence = word.confidence < 40;
    const hasReasonableLength = word.text.length >= 2 && word.text.length <= 20;
    const hasSignatureLikeAspectRatio = aspectRatio > 2 && aspectRatio < 8;

    return hasLowConfidence && hasReasonableLength && hasSignatureLikeAspectRatio;
  }

  /**
   * Cleanup resources
   */
  public async cleanup(): Promise<void> {
    logger.info('Cleaning up OCR engine resources');

    for (const worker of this.workers) {
      await worker.terminate();
    }

    this.workers = [];
    this.availableWorkers = [];
    this.busyWorkers.clear();
    this.isInitialized = false;

    if (this.cache) {
      this.cache.flushAll();
    }
  }
}
