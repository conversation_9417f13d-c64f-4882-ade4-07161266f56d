import sharp from 'sharp';
import Jim<PERSON> from 'jimp';
import jsQR from 'jsqr';
import uagga from 'quagga';
import {
  DocumentCoordinates,
  DetectedFeature,
  FeatureType,
  ImageData,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';

export interface BarcodeResult {
  type: 'qr' | 'barcode' | 'datamatrix' | 'pdf417';
  data: string;
  bounds: DocumentCoordinates;
  confidence: number;
  format?: string;
}

export interface LayoutRegion {
  type: 'text' | 'image' | 'table' | 'header' | 'footer' | 'sidebar';
  bounds: DocumentCoordinates;
  confidence: number;
  properties: Record<string, unknown>;
}

export interface DocumentLayout {
  regions: LayoutRegion[];
  readingOrder: number[];
  columns: number;
  orientation: 'portrait' | 'landscape';
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface AdvancedFeatureDetectionOptions {
  detectText: boolean;
  detectImages: boolean;
  detectTables: boolean;
  detectBarcodes: boolean;
  detectSignatures: boolean;
  detectLogos: boolean;
  detectStamps: boolean;
  analyzeLayout: boolean;
  minimumConfidence: number;
}

export interface ComputerVisionResult {
  features: DetectedFeature[];
  barcodes: BarcodeResult[];
  layout: DocumentLayout;
  textRegions: DocumentCoordinates[];
  imageRegions: DocumentCoordinates[];
  tableRegions: DocumentCoordinates[];
  processingTime: number;
  confidence: number;
}

/**
 * Advanced Computer Vision Service for document analysis
 * Provides sophisticated image analysis capabilities beyond basic OCR
 */
export class ComputerVisionService {
  private readonly defaultOptions: AdvancedFeatureDetectionOptions = {
    detectText: true,
    detectImages: true,
    detectTables: true,
    detectBarcodes: true,
    detectSignatures: true,
    detectLogos: false,
    detectStamps: true,
    analyzeLayout: true,
    minimumConfidence: 0.6,
  };

  /**
   * Perform comprehensive computer vision analysis on an image
   */
  public async analyzeDocument(
    imageBuffer: Buffer,
    options: Partial<AdvancedFeatureDetectionOptions> = {}
  ): Promise<ComputerVisionResult> {
    const startTime = Date.now();
    const analysisOptions = { ...this.defaultOptions, ...options };

    try {
      logger.debug('Starting computer vision analysis', { options: analysisOptions });

      const features: DetectedFeature[] = [];
      const barcodes: BarcodeResult[] = [];
      let layout: DocumentLayout = this.createEmptyLayout();
      const textRegions: DocumentCoordinates[] = [];
      const imageRegions: DocumentCoordinates[] = [];
      const tableRegions: DocumentCoordinates[] = [];

      // Get image metadata (for future use)
      // const imageData = await this.getImageMetadata(imageBuffer);

      // Detect text regions
      if (analysisOptions.detectText) {
        const textFeatures = await this.detectTextRegions(imageBuffer);
        features.push(...textFeatures);
        textRegions.push(...textFeatures.map(f => f.bounds));
      }

      // Detect image regions
      if (analysisOptions.detectImages) {
        const imageFeatures = await this.detectImageRegions(imageBuffer);
        features.push(...imageFeatures);
        imageRegions.push(...imageFeatures.map(f => f.bounds));
      }

      // Detect table regions
      if (analysisOptions.detectTables) {
        const tableFeatures = await this.detectTableRegions(imageBuffer);
        features.push(...tableFeatures);
        tableRegions.push(...tableFeatures.map(f => f.bounds));
      }

      // Detect barcodes and QR codes
      if (analysisOptions.detectBarcodes) {
        const detectedBarcodes = await this.detectBarcodes(imageBuffer);
        barcodes.push(...detectedBarcodes);

        // Add barcode features
        for (const barcode of detectedBarcodes) {
          features.push({
            type: FeatureType.BARCODE,
            bounds: barcode.bounds,
            confidence: barcode.confidence,
            properties: {
              type: barcode.type,
              data: barcode.data,
              format: barcode.format,
            },
          });
        }
      }

      // Detect signatures
      if (analysisOptions.detectSignatures) {
        const signatureFeatures = await this.detectSignatures(imageBuffer);
        features.push(...signatureFeatures);
      }

      // Detect stamps
      if (analysisOptions.detectStamps) {
        const stampFeatures = await this.detectStamps(imageBuffer);
        features.push(...stampFeatures);
      }

      // Analyze document layout
      if (analysisOptions.analyzeLayout) {
        layout = await this.analyzeDocumentLayout(imageBuffer, features);
      }

      // Filter features by confidence
      const filteredFeatures = features.filter(
        f => f.confidence >= analysisOptions.minimumConfidence
      );

      const processingTime = Date.now() - startTime;
      const overallConfidence = this.calculateOverallConfidence(filteredFeatures);

      logger.debug('Computer vision analysis completed', {
        featuresDetected: filteredFeatures.length,
        barcodesDetected: barcodes.length,
        processingTime,
        confidence: overallConfidence,
      });

      return {
        features: filteredFeatures,
        barcodes,
        layout,
        textRegions,
        imageRegions,
        tableRegions,
        processingTime,
        confidence: overallConfidence,
      };
    } catch (error) {
      logger.error('Computer vision analysis failed', { error });
      throw new Error(`Computer vision analysis failed: ${error.message}`);
    }
  }

  /**
   * Detect text regions using edge detection and morphological operations
   */
  private async detectTextRegions(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    try {
      const features: DetectedFeature[] = [];

      // Convert to grayscale and apply edge detection
      const processedImage = await sharp(imageBuffer).greyscale().normalize().toBuffer();

      // Use Jimp for more advanced image processing
      const jimpImage = await Jimp.read(processedImage);

      // Apply edge detection (simplified Sobel filter)
      const edgeImage = jimpImage.clone().convolute([
        [-1, -1, -1],
        [-1, 8, -1],
        [-1, -1, -1],
      ]);

      // Analyze connected components to find text-like regions
      const textRegions = await this.findConnectedComponents(edgeImage, 'text');

      for (const region of textRegions) {
        features.push({
          type: FeatureType.TEXT_BLOCK,
          bounds: region.bounds,
          confidence: region.confidence,
          properties: {
            area: region.area,
            aspectRatio: region.aspectRatio,
            density: region.density,
          },
        });
      }

      return features;
    } catch (error) {
      logger.warn('Text region detection failed', { error });
      return [];
    }
  }

  /**
   * Detect image regions using texture analysis
   */
  private async detectImageRegions(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    try {
      const features: DetectedFeature[] = [];

      // Analyze image statistics to find regions with high variance (likely images)
      const stats = await sharp(imageBuffer).stats();

      // Simple heuristic: regions with high standard deviation are likely images
      if (stats.channels?.[0]) {
        const channel = stats.channels[0];
        if (channel.std > 30) {
          // High variance suggests image content
          const metadata = await sharp(imageBuffer).metadata();
          features.push({
            type: FeatureType.IMAGE,
            bounds: {
              x: 0,
              y: 0,
              width: metadata.width || 0,
              height: metadata.height || 0,
              pageNumber: 1,
            },
            confidence: Math.min(channel.std / 100, 1.0),
            properties: {
              variance: channel.std,
              mean: channel.mean,
            },
          });
        }
      }

      return features;
    } catch (error) {
      logger.warn('Image region detection failed', { error });
      return [];
    }
  }

  /**
   * Detect table regions using line detection
   */
  private async detectTableRegions(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    try {
      const features: DetectedFeature[] = [];

      // Convert to binary image for line detection
      const binaryImage = await sharp(imageBuffer).greyscale().threshold(128).toBuffer();

      const jimpImage = await Jimp.read(binaryImage);

      // Detect horizontal and vertical lines
      const horizontalLines = await this.detectLines(jimpImage, 'horizontal');
      const verticalLines = await this.detectLines(jimpImage, 'vertical');

      // Find intersections to identify table structures
      const tableRegions = this.findTableIntersections(horizontalLines, verticalLines);

      for (const region of tableRegions) {
        features.push({
          type: FeatureType.TABLE,
          bounds: region.bounds,
          confidence: region.confidence,
          properties: {
            rows: region.rows,
            columns: region.columns,
            cellCount: region.cellCount,
          },
        });
      }

      return features;
    } catch (error) {
      logger.warn('Table region detection failed', { error });
      return [];
    }
  }

  /**
   * Detect barcodes and QR codes using jsQR and QuaggaJS
   */
  private async detectBarcodes(imageBuffer: Buffer): Promise<BarcodeResult[]> {
    try {
      const barcodes: BarcodeResult[] = [];

      // Detect QR codes using jsQR
      const qrCodes = await this.detectQRCodes(imageBuffer);
      barcodes.push(...qrCodes);

      // Detect 1D barcodes using QuaggaJS
      const oneDimensionalBarcodes = await this.detectOneDimensionalBarcodes(imageBuffer);
      barcodes.push(...oneDimensionalBarcodes);

      logger.debug('Barcode detection completed', {
        qrCodes: qrCodes.length,
        barcodes: oneDimensionalBarcodes.length,
        total: barcodes.length,
      });

      return barcodes;
    } catch (error) {
      logger.warn('Barcode detection failed', { error });
      return [];
    }
  }

  /**
   * Detect QR codes using jsQR library
   */
  private async detectQRCodes(imageBuffer: Buffer): Promise<BarcodeResult[]> {
    try {
      const qrCodes: BarcodeResult[] = [];

      // Convert image to RGBA format for jsQR
      const { data, info } = await sharp(imageBuffer)
        .ensureAlpha()
        .raw()
        .toBuffer({ resolveWithObject: true });

      // Create ImageData object for jsQR
      const imageData = {
        data: new Uint8ClampedArray(data),
        width: info.width,
        height: info.height,
      };

      // Detect QR code
      const qrResult = jsQR(imageData.data, imageData.width, imageData.height);

      if (qrResult) {
        qrCodes.push({
          type: 'qr',
          data: qrResult.data,
          bounds: {
            x: qrResult.location.topLeftCorner.x,
            y: qrResult.location.topLeftCorner.y,
            width: qrResult.location.topRightCorner.x - qrResult.location.topLeftCorner.x,
            height: qrResult.location.bottomLeftCorner.y - qrResult.location.topLeftCorner.y,
            pageNumber: 1,
          },
          confidence: 0.9, // jsQR doesn't provide confidence, use high default
        });
      }

      return qrCodes;
    } catch (error) {
      logger.warn('QR code detection failed', { error });
      return [];
    }
  }

  /**
   * Detect 1D barcodes using QuaggaJS
   */
  private async detectOneDimensionalBarcodes(imageBuffer: Buffer): Promise<BarcodeResult[]> {
    return new Promise(resolve => {
      try {
        const barcodes: BarcodeResult[] = [];

        // Convert image to format suitable for Quagga
        sharp(imageBuffer)
          .greyscale()
          .png()
          .toBuffer()
          .then(processedBuffer => {
            // Create a temporary canvas-like object for Quagga (for future use)
            // const canvas = {
            //   width: 0,
            //   height: 0,
            //   getContext: () => ({
            //     drawImage: () => {},
            //     getImageData: () => ({ data: processedBuffer }),
            //   }),
            // };

            Quagga.decodeSingle(
              {
                src: `data:image/png;base64,${processedBuffer.toString('base64')}`,
                numOfWorkers: 0,
                inputStream: {
                  size: 800,
                },
                locator: {
                  patchSize: 'medium',
                  halfSample: true,
                },
                decoder: {
                  readers: [
                    'code_128_reader',
                    'ean_reader',
                    'ean_8_reader',
                    'code_39_reader',
                    'code_39_vin_reader',
                    'codabar_reader',
                    'upc_reader',
                    'upc_e_reader',
                    'i2of5_reader',
                  ],
                },
              },
              result => {
                if (result?.codeResult) {
                  const box = result.line;
                  if (box) {
                    barcodes.push({
                      type: 'barcode',
                      data: result.codeResult.code,
                      bounds: {
                        x: Math.min(box[0].x, box[1].x),
                        y: Math.min(box[0].y, box[1].y),
                        width: Math.abs(box[1].x - box[0].x),
                        height: Math.abs(box[1].y - box[0].y),
                        pageNumber: 1,
                      },
                      confidence: 0.8, // Default confidence for successful detection
                      format: result.codeResult.format,
                    });
                  }
                }
                resolve(barcodes);
              }
            );
          })
          .catch(error => {
            logger.warn('1D barcode detection failed', { error });
            resolve([]);
          });
      } catch (error) {
        logger.warn('1D barcode detection setup failed', { error });
        resolve([]);
      }
    });
  }

  /**
   * Detect signature regions using shape analysis
   */
  private async detectSignatures(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    try {
      const features: DetectedFeature[] = [];

      // Convert to grayscale and apply edge detection
      const edgeImage = await sharp(imageBuffer)
        .greyscale()
        .convolute({
          width: 3,
          height: 3,
          kernel: [-1, -1, -1, -1, 8, -1, -1, -1, -1],
        })
        .toBuffer();

      const jimpImage = await Jimp.read(edgeImage);

      // Look for regions with signature-like characteristics
      const signatureRegions = await this.findConnectedComponents(jimpImage, 'signature');

      for (const region of signatureRegions) {
        if (this.isSignatureLike(region)) {
          features.push({
            type: FeatureType.SIGNATURE,
            bounds: region.bounds,
            confidence: region.confidence,
            properties: {
              area: region.area,
              aspectRatio: region.aspectRatio,
              complexity: region.complexity,
            },
          });
        }
      }

      return features;
    } catch (error) {
      logger.warn('Signature detection failed', { error });
      return [];
    }
  }

  /**
   * Detect stamp regions using circular/rectangular shape detection
   */
  private async detectStamps(imageBuffer: Buffer): Promise<DetectedFeature[]> {
    try {
      const features: DetectedFeature[] = [];

      // Convert to binary image for shape detection
      const binaryImage = await sharp(imageBuffer).greyscale().threshold(128).toBuffer();

      const jimpImage = await Jimp.read(binaryImage);

      // Look for circular and rectangular regions that might be stamps
      const stampRegions = await this.findConnectedComponents(jimpImage, 'stamp');

      for (const region of stampRegions) {
        if (this.isStampLike(region)) {
          features.push({
            type: FeatureType.STAMP,
            bounds: region.bounds,
            confidence: region.confidence,
            properties: {
              shape: region.shape,
              area: region.area,
              perimeter: region.perimeter,
              circularity: region.circularity,
            },
          });
        }
      }

      return features;
    } catch (error) {
      logger.warn('Stamp detection failed', { error });
      return [];
    }
  }

  /**
   * Analyze document layout and reading order
   */
  private async analyzeDocumentLayout(
    imageBuffer: Buffer,
    features: DetectedFeature[]
  ): Promise<DocumentLayout> {
    try {
      const metadata = await sharp(imageBuffer).metadata();
      const width = metadata.width || 0;
      const height = metadata.height || 0;

      // Determine orientation
      const orientation = width > height ? 'landscape' : 'portrait';

      // Estimate margins by analyzing white space
      const margins = await this.estimateMargins(imageBuffer);

      // Group features into layout regions
      const regions = this.groupFeaturesIntoRegions(features, width, height);

      // Determine reading order
      const readingOrder = this.calculateReadingOrder(regions);

      // Estimate column count
      const columns = this.estimateColumnCount(regions, width);

      return {
        regions,
        readingOrder,
        columns,
        orientation,
        margins,
      };
    } catch (error) {
      logger.warn('Layout analysis failed', { error });
      return this.createEmptyLayout();
    }
  }

  /**
   * Get image metadata
   */
  private async getImageMetadata(imageBuffer: Buffer): Promise<ImageData> {
    const metadata = await sharp(imageBuffer).metadata();
    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || 'unknown',
      data: imageBuffer.buffer.slice(
        imageBuffer.byteOffset,
        imageBuffer.byteOffset + imageBuffer.byteLength
      ),
      dpi: metadata.density,
      colorSpace: metadata.space,
    };
  }

  /**
   * Find connected components in image
   */
  private findConnectedComponents(
    image: Jimp,
    type: 'text' | 'signature' | 'stamp'
  ): Promise<
    Array<{
      bounds: DocumentCoordinates;
      confidence: number;
      area: number;
      aspectRatio: number;
      density?: number;
      complexity?: number;
      shape?: string;
      perimeter?: number;
      circularity?: number;
    }>
  > {
    const components: Array<{
      bounds: DocumentCoordinates;
      confidence: number;
      area: number;
      aspectRatio: number;
      density?: number;
      complexity?: number;
      shape?: string;
      perimeter?: number;
      circularity?: number;
    }> = [];

    // Simplified connected component analysis
    // In a real implementation, you would use proper flood-fill algorithm
    const width = image.getWidth();
    const height = image.getHeight();
    const visited = new Array(width * height).fill(false);

    for (let y = 0; y < height; y += 10) {
      for (let x = 0; x < width; x += 10) {
        const index = y * width + x;
        if (!visited[index]) {
          const component = this.floodFill(image, x, y, visited, type);
          if (component && this.isValidComponent(component, type)) {
            components.push(component);
          }
        }
      }
    }

    return components;
  }

  /**
   * Flood fill algorithm for connected component analysis
   */
  private floodFill(
    image: Jimp,
    startX: number,
    startY: number,
    visited: boolean[],
    type: 'text' | 'signature' | 'stamp'
  ): {
    bounds: DocumentCoordinates;
    confidence: number;
    area: number;
    aspectRatio: number;
    density?: number;
    complexity?: number;
    shape?: string;
    perimeter?: number;
    circularity?: number;
  } | null {
    const width = image.getWidth();
    const height = image.getHeight();
    const stack: Array<{ x: number; y: number }> = [{ x: startX, y: startY }];
    const component: Array<{ x: number; y: number }> = [];

    let minX = startX,
      maxX = startX,
      minY = startY,
      maxY = startY;

    while (stack.length > 0) {
      const point = stack.pop();
      if (!point) continue;
      const { x, y } = point;
      const index = y * width + x;

      if (x < 0 || x >= width || y < 0 || y >= height || visited[index]) {
        continue;
      }

      const pixel = image.getPixelColor(x, y);
      const gray = Jimp.intToRGBA(pixel);

      // Check if pixel is part of foreground (dark pixels for text/signatures)
      if (gray.r > 128) continue; // Skip white/light pixels

      visited[index] = true;
      component.push({ x, y });

      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);

      // Add neighbors to stack
      stack.push({ x: x + 1, y });
      stack.push({ x: x - 1, y });
      stack.push({ x, y: y + 1 });
      stack.push({ x, y: y - 1 });
    }

    if (component.length < 10) return null; // Too small to be meaningful

    const bounds: DocumentCoordinates = {
      x: minX,
      y: minY,
      width: maxX - minX + 1,
      height: maxY - minY + 1,
      pageNumber: 1,
    };

    const area = component.length;
    const aspectRatio = bounds.width / bounds.height;
    const density = area / (bounds.width * bounds.height);

    // Calculate additional properties based on type
    let complexity = 0;
    let shape = 'unknown';
    let perimeter = 0;
    let circularity = 0;

    if (type === 'signature') {
      complexity = this.calculateComplexity(component);
    } else if (type === 'stamp') {
      perimeter = this.calculatePerimeter(component);
      circularity = (4 * Math.PI * area) / (perimeter * perimeter);
      shape = circularity > 0.7 ? 'circular' : 'rectangular';
    }

    return {
      bounds,
      confidence: Math.min(density * 2, 1.0), // Simple confidence based on density
      area,
      aspectRatio,
      density,
      complexity,
      shape,
      perimeter,
      circularity,
    };
  }

  /**
   * Calculate complexity of a component (useful for signature detection)
   */
  private calculateComplexity(component: Array<{ x: number; y: number }>): number {
    if (component.length < 2) return 0;

    let totalDistance = 0;
    for (let i = 1; i < component.length; i++) {
      const dx = component[i].x - component[i - 1].x;
      const dy = component[i].y - component[i - 1].y;
      totalDistance += Math.sqrt(dx * dx + dy * dy);
    }

    return totalDistance / component.length;
  }

  /**
   * Calculate perimeter of a component
   */
  private calculatePerimeter(component: Array<{ x: number; y: number }>): number {
    const uniquePoints = new Set(component.map(p => `${p.x},${p.y}`));
    let perimeter = 0;

    for (const point of component) {
      const { x, y } = point;
      const neighbors = [
        { x: x + 1, y },
        { x: x - 1, y },
        { x, y: y + 1 },
        { x, y: y - 1 },
      ];

      for (const neighbor of neighbors) {
        if (!uniquePoints.has(`${neighbor.x},${neighbor.y}`)) {
          perimeter++;
          break;
        }
      }
    }

    return perimeter;
  }

  /**
   * Check if component is valid for the given type
   */
  private isValidComponent(
    component: {
      bounds: DocumentCoordinates;
      confidence: number;
      area: number;
      aspectRatio: number;
      density?: number;
      complexity?: number;
      shape?: string;
      perimeter?: number;
      circularity?: number;
    },
    type: 'text' | 'signature' | 'stamp'
  ): boolean {
    switch (type) {
      case 'text':
        return (
          component.area > 50 &&
          component.aspectRatio > 0.1 &&
          component.aspectRatio < 10 &&
          (component.density || 0) > 0.1
        );
      case 'signature':
        return (
          component.area > 100 &&
          component.aspectRatio > 0.5 &&
          component.aspectRatio < 5 &&
          (component.complexity || 0) > 2
        );
      case 'stamp':
        return (
          component.area > 200 &&
          component.aspectRatio > 0.3 &&
          component.aspectRatio < 3 &&
          (component.circularity || 0) > 0.3
        );
      default:
        return false;
    }
  }

  /**
   * Check if component has signature-like characteristics
   */
  private isSignatureLike(component: {
    bounds: DocumentCoordinates;
    confidence: number;
    area: number;
    aspectRatio: number;
    complexity?: number;
  }): boolean {
    return (
      component.area > 100 &&
      component.area < 10000 &&
      component.aspectRatio > 1 &&
      component.aspectRatio < 8 &&
      (component.complexity || 0) > 3
    );
  }

  /**
   * Check if component has stamp-like characteristics
   */
  private isStampLike(component: {
    bounds: DocumentCoordinates;
    confidence: number;
    area: number;
    aspectRatio: number;
    circularity?: number;
    shape?: string;
  }): boolean {
    return (
      component.area > 500 &&
      component.area < 50000 &&
      component.aspectRatio > 0.5 &&
      component.aspectRatio < 2 &&
      ((component.circularity || 0) > 0.5 || component.shape === 'circular')
    );
  }

  /**
   * Detect lines in image (horizontal or vertical)
   */
  private detectLines(
    image: Jimp,
    direction: 'horizontal' | 'vertical'
  ): Promise<
    Array<{ start: { x: number; y: number }; end: { x: number; y: number }; length: number }>
  > {
    const lines: Array<{
      start: { x: number; y: number };
      end: { x: number; y: number };
      length: number;
    }> = [];
    const width = image.getWidth();
    const height = image.getHeight();

    if (direction === 'horizontal') {
      for (let y = 0; y < height; y += 5) {
        let lineStart = -1;
        let lineLength = 0;

        for (let x = 0; x < width; x++) {
          const pixel = image.getPixelColor(x, y);
          const gray = Jimp.intToRGBA(pixel);

          if (gray.r < 128) {
            // Dark pixel (line)
            if (lineStart === -1) lineStart = x;
            lineLength++;
          } else {
            if (lineLength > 20) {
              // Minimum line length
              lines.push({
                start: { x: lineStart, y },
                end: { x: lineStart + lineLength, y },
                length: lineLength,
              });
            }
            lineStart = -1;
            lineLength = 0;
          }
        }

        if (lineLength > 20) {
          lines.push({
            start: { x: lineStart, y },
            end: { x: lineStart + lineLength, y },
            length: lineLength,
          });
        }
      }
    } else {
      for (let x = 0; x < width; x += 5) {
        let lineStart = -1;
        let lineLength = 0;

        for (let y = 0; y < height; y++) {
          const pixel = image.getPixelColor(x, y);
          const gray = Jimp.intToRGBA(pixel);

          if (gray.r < 128) {
            // Dark pixel (line)
            if (lineStart === -1) lineStart = y;
            lineLength++;
          } else {
            if (lineLength > 20) {
              // Minimum line length
              lines.push({
                start: { x, y: lineStart },
                end: { x, y: lineStart + lineLength },
                length: lineLength,
              });
            }
            lineStart = -1;
            lineLength = 0;
          }
        }

        if (lineLength > 20) {
          lines.push({
            start: { x, y: lineStart },
            end: { x, y: lineStart + lineLength },
            length: lineLength,
          });
        }
      }
    }

    return lines;
  }

  /**
   * Find table intersections from horizontal and vertical lines
   */
  private findTableIntersections(
    horizontalLines: Array<{
      start: { x: number; y: number };
      end: { x: number; y: number };
      length: number;
    }>,
    verticalLines: Array<{
      start: { x: number; y: number };
      end: { x: number; y: number };
      length: number;
    }>
  ): Array<{
    bounds: DocumentCoordinates;
    confidence: number;
    rows: number;
    columns: number;
    cellCount: number;
  }> {
    const tables: Array<{
      bounds: DocumentCoordinates;
      confidence: number;
      rows: number;
      columns: number;
      cellCount: number;
    }> = [];

    if (horizontalLines.length < 2 || verticalLines.length < 2) {
      return tables;
    }

    // Find intersections
    const intersections: Array<{ x: number; y: number }> = [];

    for (const hLine of horizontalLines) {
      for (const vLine of verticalLines) {
        const hY = hLine.start.y;
        const vX = vLine.start.x;

        // Check if lines intersect
        if (vX >= hLine.start.x && vX <= hLine.end.x && hY >= vLine.start.y && hY <= vLine.end.y) {
          intersections.push({ x: vX, y: hY });
        }
      }
    }

    if (intersections.length >= 4) {
      // Find bounding box of intersections
      const minX = Math.min(...intersections.map(p => p.x));
      const maxX = Math.max(...intersections.map(p => p.x));
      const minY = Math.min(...intersections.map(p => p.y));
      const maxY = Math.max(...intersections.map(p => p.y));

      const uniqueXs = [...new Set(intersections.map(p => p.x))].sort((a, b) => a - b);
      const uniqueYs = [...new Set(intersections.map(p => p.y))].sort((a, b) => a - b);

      const columns = uniqueXs.length - 1;
      const rows = uniqueYs.length - 1;

      if (rows > 0 && columns > 0) {
        tables.push({
          bounds: {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
            pageNumber: 1,
          },
          confidence: Math.min(intersections.length / (rows * columns), 1.0),
          rows,
          columns,
          cellCount: rows * columns,
        });
      }
    }

    return tables;
  }

  /**
   * Estimate document margins by analyzing white space
   */
  private async estimateMargins(imageBuffer: Buffer): Promise<{
    top: number;
    right: number;
    bottom: number;
    left: number;
  }> {
    try {
      const metadata = await sharp(imageBuffer).metadata();
      const width = metadata.width || 0;
      const height = metadata.height || 0;

      // Convert to grayscale for analysis
      const grayBuffer = await sharp(imageBuffer).greyscale().raw().toBuffer();

      // Analyze edges to find content boundaries
      let topMargin = 0;
      let bottomMargin = 0;
      let leftMargin = 0;
      let rightMargin = 0;

      // Find top margin
      for (let y = 0; y < height; y++) {
        let hasContent = false;
        for (let x = 0; x < width; x++) {
          const pixelIndex = y * width + x;
          if (grayBuffer[pixelIndex] < 200) {
            // Non-white pixel
            hasContent = true;
            break;
          }
        }
        if (hasContent) {
          topMargin = y;
          break;
        }
      }

      // Find bottom margin
      for (let y = height - 1; y >= 0; y--) {
        let hasContent = false;
        for (let x = 0; x < width; x++) {
          const pixelIndex = y * width + x;
          if (grayBuffer[pixelIndex] < 200) {
            // Non-white pixel
            hasContent = true;
            break;
          }
        }
        if (hasContent) {
          bottomMargin = height - y - 1;
          break;
        }
      }

      // Find left margin
      for (let x = 0; x < width; x++) {
        let hasContent = false;
        for (let y = 0; y < height; y++) {
          const pixelIndex = y * width + x;
          if (grayBuffer[pixelIndex] < 200) {
            // Non-white pixel
            hasContent = true;
            break;
          }
        }
        if (hasContent) {
          leftMargin = x;
          break;
        }
      }

      // Find right margin
      for (let x = width - 1; x >= 0; x--) {
        let hasContent = false;
        for (let y = 0; y < height; y++) {
          const pixelIndex = y * width + x;
          if (grayBuffer[pixelIndex] < 200) {
            // Non-white pixel
            hasContent = true;
            break;
          }
        }
        if (hasContent) {
          rightMargin = width - x - 1;
          break;
        }
      }

      return {
        top: topMargin,
        right: rightMargin,
        bottom: bottomMargin,
        left: leftMargin,
      };
    } catch (error) {
      logger.warn('Margin estimation failed', { error });
      return { top: 0, right: 0, bottom: 0, left: 0 };
    }
  }

  /**
   * Group features into layout regions
   */
  private groupFeaturesIntoRegions(
    features: DetectedFeature[],
    width: number,
    height: number
  ): LayoutRegion[] {
    const regions: LayoutRegion[] = [];

    // Group features by type and proximity
    const textFeatures = features.filter(f => f.type === FeatureType.TEXT_BLOCK);
    const imageFeatures = features.filter(f => f.type === FeatureType.IMAGE);
    const tableFeatures = features.filter(f => f.type === FeatureType.TABLE);

    // Create text regions
    for (const feature of textFeatures) {
      regions.push({
        type: 'text',
        bounds: feature.bounds,
        confidence: feature.confidence,
        properties: feature.properties,
      });
    }

    // Create image regions
    for (const feature of imageFeatures) {
      regions.push({
        type: 'image',
        bounds: feature.bounds,
        confidence: feature.confidence,
        properties: feature.properties,
      });
    }

    // Create table regions
    for (const feature of tableFeatures) {
      regions.push({
        type: 'table',
        bounds: feature.bounds,
        confidence: feature.confidence,
        properties: feature.properties,
      });
    }

    // Detect header and footer regions
    const headerRegions = regions.filter(r => r.bounds.y < height * 0.15);
    const footerRegions = regions.filter(r => r.bounds.y > height * 0.85);

    for (const region of headerRegions) {
      region.type = 'header';
    }

    for (const region of footerRegions) {
      region.type = 'footer';
    }

    return regions;
  }

  /**
   * Calculate reading order for layout regions
   */
  private calculateReadingOrder(regions: LayoutRegion[]): number[] {
    // Sort regions by position (top to bottom, left to right)
    const sortedRegions = regions
      .map((region, index) => ({ region, index }))
      .sort((a, b) => {
        const yDiff = a.region.bounds.y - b.region.bounds.y;
        if (Math.abs(yDiff) < 20) {
          // Same line
          return a.region.bounds.x - b.region.bounds.x;
        }
        return yDiff;
      });

    return sortedRegions.map(item => item.index);
  }

  /**
   * Estimate number of columns in document
   */
  private estimateColumnCount(regions: LayoutRegion[], width: number): number {
    const textRegions = regions.filter(r => r.type === 'text');

    if (textRegions.length === 0) return 1;

    // Group regions by horizontal position
    const columnGroups: LayoutRegion[][] = [];
    const tolerance = width * 0.1; // 10% tolerance

    for (const region of textRegions) {
      let addedToGroup = false;

      for (const group of columnGroups) {
        const groupCenterX =
          group.reduce((sum, r) => sum + r.bounds.x + r.bounds.width / 2, 0) / group.length;
        const regionCenterX = region.bounds.x + region.bounds.width / 2;

        if (Math.abs(groupCenterX - regionCenterX) < tolerance) {
          group.push(region);
          addedToGroup = true;
          break;
        }
      }

      if (!addedToGroup) {
        columnGroups.push([region]);
      }
    }

    return Math.max(1, columnGroups.length);
  }

  /**
   * Create empty layout structure
   */
  private createEmptyLayout(): DocumentLayout {
    return {
      regions: [],
      readingOrder: [],
      columns: 1,
      orientation: 'portrait',
      margins: { top: 0, right: 0, bottom: 0, left: 0 },
    };
  }

  /**
   * Calculate overall confidence from features
   */
  private calculateOverallConfidence(features: DetectedFeature[]): number {
    if (features.length === 0) return 0;

    const totalConfidence = features.reduce((sum, feature) => sum + feature.confidence, 0);
    return totalConfidence / features.length;
  }
}
