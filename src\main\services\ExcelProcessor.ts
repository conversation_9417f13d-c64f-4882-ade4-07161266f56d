/**
 * Excel document processor using ExcelJS
 * Handles .xlsx, .xls, and .xlsm files with comprehensive data extraction
 */

import * as ExcelJS from 'exceljs';
import {
  CalculationResult,
  DocumentTable,
  DocumentType,
  ExtractedData,
  ExtractedDataType,
  ExtractionMethod,
  ProcessingOptions,
  ValidationResult,
} from '../../shared/types/Document';
import { logger } from '../utils/logger';
import { DocumentProcessingError, DocumentProcessor } from './DocumentProcessor';

// Excel-specific interfaces
export interface ExcelWorksheetInfo {
  id: number;
  name: string;
  state: string;
  rowCount: number;
  columnCount: number;
  hasData: boolean;
  isHidden: boolean;
}

export interface ExcelCellInfo {
  address: string;
  value: unknown;
  type: ExcelJS.ValueType;
  formula?: string;
  result?: unknown;
  style?: ExcelJS.Style;
  hyperlink?: string;
  comment?: string;
}

export interface ExcelTableInfo {
  name?: string;
  range: string;
  headers: string[];
  data: unknown[][];
  rowCount: number;
  columnCount: number;
  hasHeaders: boolean;
}

export interface ExcelFormatInfo {
  font?: ExcelJS.Font;
  fill?: ExcelJS.Fill;
  border?: ExcelJS.Borders;
  alignment?: ExcelJS.Alignment;
  numFmt?: string;
  protection?: ExcelJS.Protection;
}

export interface DataCleaningOptions {
  trimWhitespace?: boolean;
  normalizeCase?: 'upper' | 'lower' | 'title' | 'none';
  removeEmptyRows?: boolean;
  removeEmptyColumns?: boolean;
  handleMissingValues?: 'skip' | 'default' | 'interpolate';
  defaultValue?: unknown;
}

export interface DataCleaningResult {
  cleanedValues: unknown[];
  changedCount: number;
  removedRows: number[];
  removedColumns: number[];
  issues: string[];
}

export interface MissingValueAnalysis {
  missingCount: number;
  missingRate: number;
  missingCells: string[];
  severity: 'low' | 'medium' | 'high';
  recommendations: string[];
}

export interface ColumnStatistics {
  column: number;
  name?: string;
  dataType: 'number' | 'text' | 'date' | 'boolean' | 'formula' | 'mixed';
  uniqueValues: number;
  nullCount: number;
  min?: number;
  max?: number;
  mean?: number;
  median?: number;
  mode?: unknown;
  standardDeviation?: number;
}

export class ExcelProcessor extends DocumentProcessor {
  private workbook: ExcelJS.Workbook | null = null;
  private readonly maxCellsToProcess = 1000000; // 1M cells limit
  private readonly maxWorksheets = 100;

  constructor() {
    super('ExcelProcessor', {
      supportedTypes: [DocumentType.EXCEL],
      canExtractText: true,
      canExtractImages: false, // Images in Excel require special handling
      canExtractTables: true,
      canDetectFormFields: false, // Excel doesn't have traditional form fields
      canPerformOCR: false,
      canPreserveFormatting: true,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportedEncodings: ['utf-8'],
      requiresNetwork: false,
      processingTimeEstimate: 1, // seconds per MB
    });
  }

  /**
   * Extract text content from Excel document
   */
  public async extractText(buffer: Buffer, options: ProcessingOptions): Promise<string> {
    try {
      await this.loadWorkbook(buffer);
      if (!this.workbook) {
        throw new Error('Failed to load workbook');
      }

      const textParts: string[] = [];

      // Process each worksheet
      this.workbook.eachSheet((worksheet, _sheetId) => {
        if (worksheet.state === 'hidden' && !options.includeHiddenContent) {
          return;
        }

        textParts.push(`\n=== Sheet: ${worksheet.name} ===\n`);

        // Extract text from cells
        worksheet.eachRow((row, _rowNumber) => {
          const rowText: string[] = [];
          row.eachCell((cell, _colNumber) => {
            const cellValue = this.getCellDisplayValue(cell);
            if (cellValue && cellValue.toString().trim()) {
              rowText.push(cellValue.toString());
            }
          });

          if (rowText.length > 0) {
            textParts.push(rowText.join('\t'));
          }
        });
      });

      return textParts.join('\n');
    } catch (error) {
      logger.error('Failed to extract text from Excel document', { error });
      throw new DocumentProcessingError(
        'Failed to extract text from Excel document',
        'EXCEL_TEXT_EXTRACTION_FAILED',
        '',
        'text_extraction'
      );
    } finally {
      this.cleanup();
    }
  }

  /**
   * Extract structured data from Excel document
   */
  public async extractStructuredData(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<ExtractedData[]> {
    try {
      await this.loadWorkbook(buffer);
      if (!this.workbook) {
        throw new Error('Failed to load workbook');
      }

      const extractedData: ExtractedData[] = [];

      // Extract workbook metadata
      const metadata = this.extractWorkbookMetadata();
      extractedData.push({
        id: 'workbook_metadata',
        documentId: '', // Will be set by caller
        type: ExtractedDataType.TEXT,
        content: JSON.stringify(metadata),
        confidence: 1.0,
        extractionMethod: ExtractionMethod.EXCEL_PARSER,
        createdAt: new Date(),
      });

      // Process each worksheet
      this.workbook.eachSheet((worksheet, sheetId) => {
        if (worksheet.state === 'hidden' && !options.includeHiddenContent) {
          return;
        }

        // Extract worksheet info
        const worksheetInfo = this.extractWorksheetInfo(worksheet);
        extractedData.push({
          id: `worksheet_${sheetId}_info`,
          documentId: '',
          type: ExtractedDataType.TEXT,
          content: JSON.stringify(worksheetInfo),
          confidence: 1.0,
          extractionMethod: ExtractionMethod.EXCEL_PARSER,
          createdAt: new Date(),
        });

        // Extract tables if requested
        if (options.extractTables) {
          const tables = this.extractTablesFromWorksheet(worksheet);
          tables.forEach((table, index) => {
            extractedData.push({
              id: `worksheet_${sheetId}_table_${index}`,
              documentId: '',
              type: ExtractedDataType.TABLE,
              content: table,
              confidence: 0.9,
              extractionMethod: ExtractionMethod.EXCEL_PARSER,
              createdAt: new Date(),
            });
          });
        }

        // Extract formulas and calculations
        const calculations = this.extractCalculations(worksheet);
        calculations.forEach((calc, index) => {
          extractedData.push({
            id: `worksheet_${sheetId}_calculation_${index}`,
            documentId: '',
            type: ExtractedDataType.CALCULATION,
            content: calc,
            confidence: 0.95,
            extractionMethod: ExtractionMethod.EXCEL_PARSER,
            createdAt: new Date(),
          });
        });
      });

      return extractedData;
    } catch (error) {
      logger.error('Failed to extract structured data from Excel document', { error });
      throw new DocumentProcessingError(
        'Failed to extract structured data from Excel document',
        'EXCEL_STRUCTURED_EXTRACTION_FAILED',
        '',
        'structured_extraction'
      );
    } finally {
      this.cleanup();
    }
  }

  /**
   * Validate extracted data
   */
  public async validateExtraction(extractedData: ExtractedData[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    for (const data of extractedData) {
      const result: ValidationResult = {
        fieldId: data.id,
        isValid: true,
        errors: [],
        warnings: [],
      };

      // Validate based on data type
      switch (data.type) {
        case ExtractedDataType.TABLE:
          this.validateTableData(data, result);
          break;
        case ExtractedDataType.CALCULATION:
          this.validateCalculationData(data, result);
          break;
        case ExtractedDataType.TEXT:
          this.validateTextData(data, result);
          break;
      }

      results.push(result);
    }

    return Promise.resolve(results);
  }

  /**
   * Load Excel workbook from buffer with error handling
   */
  private async loadWorkbook(buffer: Buffer): Promise<void> {
    try {
      this.workbook = new ExcelJS.Workbook();

      // Set memory optimization options
      this.workbook.calcProperties.fullCalcOnLoad = true;

      await this.workbook.xlsx.load(buffer);

      // Validate workbook
      if (this.workbook.worksheets.length === 0) {
        throw new Error('Workbook contains no worksheets');
      }

      if (this.workbook.worksheets.length > this.maxWorksheets) {
        logger.warn(
          `Workbook has ${this.workbook.worksheets.length} worksheets, limiting to ${this.maxWorksheets}`
        );
      }
    } catch (error) {
      logger.error('Failed to load Excel workbook', { error });
      throw new DocumentProcessingError(
        'Failed to load Excel workbook',
        'EXCEL_WORKBOOK_LOAD_FAILED',
        '',
        'workbook_loading'
      );
    }
  }

  /**
   * Get display value from Excel cell
   */
  private getCellDisplayValue(cell: ExcelJS.Cell): unknown {
    if (cell.formula) {
      // Return calculated result if available, otherwise the formula
      return cell.result ?? cell.formula;
    }

    if (cell.value === null || cell.value === undefined) {
      return '';
    }

    // Handle different value types
    if (typeof cell.value === 'object' && cell.value !== null) {
      if (Object.hasOwn(cell.value, 'text')) {
        return (cell.value as { text: unknown }).text;
      }
      if (Object.hasOwn(cell.value, 'result')) {
        return (cell.value as { result: unknown }).result;
      }
    }

    return cell.value;
  }

  /**
   * Extract workbook metadata
   */
  private extractWorkbookMetadata(): Record<string, unknown> {
    if (!this.workbook) return {};

    return {
      creator: this.workbook.creator,
      lastModifiedBy: this.workbook.lastModifiedBy,
      created: this.workbook.created,
      modified: this.workbook.modified,
      lastPrinted: this.workbook.lastPrinted,
      category: this.workbook.category,
      manager: this.workbook.manager,
      company: this.workbook.company,
      worksheetCount: this.workbook.worksheets.length,
      definedNames: this.workbook.definedNames?.model || [],
      calcProperties: this.workbook.calcProperties,
    };
  }

  /**
   * Extract worksheet information
   */
  private extractWorksheetInfo(worksheet: ExcelJS.Worksheet): ExcelWorksheetInfo {
    const actualRowCount = worksheet.actualRowCount || 0;
    const actualColumnCount = worksheet.actualColumnCount || 0;

    return {
      id: worksheet.id,
      name: worksheet.name,
      state: worksheet.state,
      rowCount: actualRowCount,
      columnCount: actualColumnCount,
      hasData: actualRowCount > 0 && actualColumnCount > 0,
      isHidden: worksheet.state === 'hidden',
    };
  }

  /**
   * Extract tables from worksheet
   */
  private extractTablesFromWorksheet(worksheet: ExcelJS.Worksheet): DocumentTable[] {
    const tables: DocumentTable[] = [];

    try {
      // Try to detect data ranges automatically
      const dataRanges = this.detectDataRanges(worksheet);

      dataRanges.forEach(range => {
        const table = this.extractTableFromRange(worksheet, range);
        if (table && table.rows.length > 0) {
          tables.push(table);
        }
      });

      // Note: ExcelJS worksheet.tables property may not be available in all versions
      // This would require additional implementation for Excel table objects
    } catch (error) {
      logger.warn('Error extracting tables from worksheet', {
        worksheet: worksheet.name,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    return tables;
  }

  /**
   * Detect data ranges in worksheet
   */
  private detectDataRanges(worksheet: ExcelJS.Worksheet): string[] {
    const ranges: string[] = [];

    if (!worksheet.actualRowCount || !worksheet.actualColumnCount) {
      return ranges;
    }

    // Simple approach: find continuous data blocks
    const startRow = 1;
    const endRow = Math.min(worksheet.actualRowCount, 1000); // Limit for performance
    const startCol = 1;
    const endCol = Math.min(worksheet.actualColumnCount, 100); // Limit for performance

    // Find actual data boundaries
    let hasData = false;
    for (let row = startRow; row <= endRow; row++) {
      for (let col = startCol; col <= endCol; col++) {
        const cell = worksheet.getCell(row, col);
        if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
          hasData = true;
          break;
        }
      }
      if (hasData) break;
    }

    if (hasData) {
      // Create a range covering the data area
      const range = `${this.columnNumberToLetter(startCol)}${startRow}:${this.columnNumberToLetter(endCol)}${endRow}`;
      ranges.push(range);
    }

    return ranges;
  }

  /**
   * Extract table from a specific range
   */
  private extractTableFromRange(worksheet: ExcelJS.Worksheet, range: string): DocumentTable | null {
    try {
      // This is a simplified implementation - in practice, you'd parse the range properly
      const rows: unknown[][] = [];
      const headers: string[] = [];

      // For now, extract first 100 rows to avoid performance issues
      const maxRows = Math.min(worksheet.actualRowCount || 0, 100);
      const maxCols = Math.min(worksheet.actualColumnCount || 0, 50);

      // Detect header row
      const headerRow = this.detectHeaderRow(worksheet, 1, maxCols, 1, maxRows);

      // Extract headers
      if (headerRow > 0) {
        for (let col = 1; col <= maxCols; col++) {
          const cell = worksheet.getCell(headerRow, col);
          const value = this.getCellDisplayValue(cell);
          headers.push(value ? value.toString() : `Column ${col}`);
        }
      }

      // Extract data rows
      const startDataRow = headerRow > 0 ? headerRow + 1 : 1;
      for (let row = startDataRow; row <= maxRows; row++) {
        const rowData: unknown[] = [];
        let hasData = false;

        for (let col = 1; col <= maxCols; col++) {
          const cell = worksheet.getCell(row, col);
          const value = this.getCellDisplayValue(cell);
          rowData.push(value);
          if (value !== null && value !== undefined && value !== '') {
            hasData = true;
          }
        }

        if (hasData) {
          rows.push(rowData);
        }
      }

      if (rows.length === 0) {
        return null;
      }

      // Convert rows to proper TableRow format
      const tableRows = rows.map((row, rowIndex) => ({
        cells: row.map((cellValue, colIndex) => ({
          value: cellValue ? cellValue.toString() : '',
          type: this.inferCellType(cellValue) as 'text' | 'number' | 'date' | 'boolean',
          bounds: {
            x: colIndex * 100, // Approximate cell width
            y: rowIndex * 20, // Approximate cell height
            width: 100,
            height: 20,
            pageNumber: worksheet.id,
          },
        })),
      }));

      return {
        id: `table_${worksheet.id}_${Date.now()}`,
        pageNumber: worksheet.id,
        bounds: {
          x: 0,
          y: 0,
          width: maxCols * 100,
          height: rows.length * 20,
          pageNumber: worksheet.id,
        },
        rows: tableRows,
        ...(headers.length > 0 && { headers }),
      };
    } catch (error) {
      logger.warn('Error extracting table from range', {
        range,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Convert column number to letter (1 = A, 2 = B, etc.)
   */
  private columnNumberToLetter(columnNumber: number): string {
    let result = '';
    while (columnNumber > 0) {
      columnNumber--;
      result = String.fromCharCode(65 + (columnNumber % 26)) + result;
      columnNumber = Math.floor(columnNumber / 26);
    }
    return result;
  }

  /**
   * Detect header row in worksheet
   */
  public detectHeaderRow(
    worksheet: ExcelJS.Worksheet,
    startCol: number,
    endCol: number,
    startRow: number,
    endRow: number
  ): number {
    // Look for the first row that contains mostly text values
    for (let row = startRow; row <= Math.min(endRow, startRow + 5); row++) {
      let textCount = 0;
      let totalCells = 0;

      for (let col = startCol; col <= endCol; col++) {
        const cell = worksheet.getCell(row, col);
        const value = this.getCellDisplayValue(cell);

        if (value !== null && value !== undefined && value !== '') {
          totalCells++;
          if (typeof value === 'string' || cell.type === ExcelJS.ValueType.String) {
            textCount++;
          }
        }
      }

      // If more than 70% of cells are text and we have at least 2 cells, consider it a header
      if (totalCells >= 2 && textCount / totalCells > 0.7) {
        return row;
      }
    }

    return 0; // No header detected
  }

  /**
   * Extract calculations and formulas from worksheet
   */
  private extractCalculations(worksheet: ExcelJS.Worksheet): CalculationResult[] {
    const calculations: CalculationResult[] = [];

    worksheet.eachRow(row => {
      row.eachCell(cell => {
        if (cell.formula) {
          const calculation: CalculationResult = {
            formula: cell.formula,
            result: (this.getCellDisplayValue(cell) as string | number) || 0,
            variables: this.extractFormulaVariables(cell.formula),
            confidence: 0.95,
          };

          calculations.push(calculation);
        }
      });
    });

    return calculations;
  }

  /**
   * Extract variables from formula
   */
  private extractFormulaVariables(formula: string): Record<string, number | string> {
    const variables: Record<string, number | string> = {};

    // Simple regex to find cell references (A1, B2, etc.)
    const cellRefRegex = /[A-Z]+\d+/g;
    const matches = formula.match(cellRefRegex);

    if (matches) {
      matches.forEach(match => {
        variables[match] = match; // Store cell reference as variable
      });
    }

    return variables;
  }

  /**
   * Infer cell data type
   */
  private inferCellType(value: unknown): string {
    if (value === null || value === undefined || value === '') {
      return 'text';
    }

    if (typeof value === 'number') {
      return 'number';
    }

    if (typeof value === 'boolean') {
      return 'boolean';
    }

    if (value instanceof Date) {
      return 'date';
    }

    // Check if string represents a number
    if (typeof value === 'string' && !isNaN(Number(value)) && !isNaN(parseFloat(value))) {
      return 'number';
    }

    // Check if string represents a date
    if (typeof value === 'string' && !isNaN(Date.parse(value))) {
      return 'date';
    }

    return 'text';
  }

  /**
   * Validate table data
   */
  private validateTableData(data: ExtractedData, result: ValidationResult): void {
    try {
      const table = data.content as DocumentTable;

      if (!table.rows || table.rows.length === 0) {
        result.errors.push({
          code: 'EMPTY_TABLE',
          message: 'Table has no data rows',
          severity: 'error',
        });
        result.isValid = false;
      }

      if (table.headers && table.rows.length > 0 && table.rows[0]) {
        const headerCount = table.headers.length;
        const cellCount = table.rows[0].cells.length;

        if (headerCount !== cellCount) {
          result.warnings.push({
            code: 'HEADER_MISMATCH',
            message: `Header count (${headerCount}) doesn't match cell count (${cellCount})`,
          });
        }
      }
    } catch (error) {
      result.errors.push({
        code: 'TABLE_VALIDATION_ERROR',
        message: `Error validating table: ${error instanceof Error ? error.message : String(error)}`,
        severity: 'error',
      });
      result.isValid = false;
    }
  }

  /**
   * Validate calculation data
   */
  private validateCalculationData(data: ExtractedData, result: ValidationResult): void {
    try {
      const calc = data.content as CalculationResult;

      if (!calc.formula) {
        result.errors.push({
          code: 'MISSING_FORMULA',
          message: 'Calculation is missing formula',
          severity: 'error',
        });
        result.isValid = false;
      }

      if (calc.result === undefined || calc.result === null) {
        result.warnings.push({
          code: 'NO_RESULT',
          message: 'Formula has no calculated result',
        });
      }
    } catch (error) {
      result.errors.push({
        code: 'CALCULATION_VALIDATION_ERROR',
        message: `Error validating calculation: ${error instanceof Error ? error.message : String(error)}`,
        severity: 'error',
      });
      result.isValid = false;
    }
  }

  /**
   * Validate text data
   */
  private validateTextData(data: ExtractedData, result: ValidationResult): void {
    try {
      const text = data.content as string;

      if (!text || text.trim().length === 0) {
        result.warnings.push({
          code: 'EMPTY_TEXT',
          message: 'Text content is empty',
        });
      }
    } catch (error) {
      result.errors.push({
        code: 'TEXT_VALIDATION_ERROR',
        message: `Error validating text: ${error instanceof Error ? error.message : String(error)}`,
        severity: 'error',
      });
      result.isValid = false;
    }
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    this.workbook = null;
  }

  // Additional methods for data analysis and cleaning (referenced in tests)

  /**
   * Clean column data according to specified options
   */
  public cleanColumnData(
    worksheet: ExcelJS.Worksheet,
    column: number,
    startRow: number,
    endRow: number,
    options: DataCleaningOptions
  ): DataCleaningResult {
    const cleanedValues: unknown[] = [];
    let changedCount = 0;
    const issues: string[] = [];

    for (let row = startRow; row <= endRow; row++) {
      const cell = worksheet.getCell(row, column);
      let value = this.getCellDisplayValue(cell);

      if (options.trimWhitespace && typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed !== value) {
          changedCount++;
          value = trimmed;
        }
      }

      if (options.normalizeCase && typeof value === 'string') {
        let normalized: string;
        switch (options.normalizeCase) {
          case 'upper':
            normalized = value.toUpperCase();
            break;
          case 'lower':
            normalized = value.toLowerCase();
            break;
          case 'title':
            normalized = value.replace(
              /\w\S*/g,
              txt => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
            );
            break;
          default:
            normalized = value;
        }

        if (normalized !== value) {
          changedCount++;
          value = normalized;
        }
      }

      cleanedValues.push(value);
    }

    return {
      cleanedValues,
      changedCount,
      removedRows: [],
      removedColumns: [],
      issues,
    };
  }

  /**
   * Detect missing values in a range
   */
  public detectMissingValues(worksheet: ExcelJS.Worksheet, range: string): MissingValueAnalysis {
    // Parse range (simplified - assumes format like "A1:A3")
    const [startCell, endCell] = range.split(':');
    if (!startCell) {
      throw new Error('Invalid range format');
    }
    const startCol = startCell.match(/[A-Z]+/)?.[0] || 'A';
    const startRow = parseInt(startCell.match(/\d+/)?.[0] || '1');
    const endCol = endCell?.match(/[A-Z]+/)?.[0] || startCol;
    const endRow = parseInt(endCell?.match(/\d+/)?.[0] || startRow.toString());

    const startColNum = this.columnLetterToNumber(startCol);
    const endColNum = this.columnLetterToNumber(endCol);

    let totalCells = 0;
    let missingCount = 0;
    const missingCells: string[] = [];

    for (let row = startRow; row <= endRow; row++) {
      for (let col = startColNum; col <= endColNum; col++) {
        totalCells++;
        const cell = worksheet.getCell(row, col);
        const value = this.getCellDisplayValue(cell);

        if (value === null || value === undefined || value === '') {
          missingCount++;
          missingCells.push(`${this.columnNumberToLetter(col)}${row}`);
        }
      }
    }

    const missingRate = totalCells > 0 ? missingCount / totalCells : 0;
    let severity: 'low' | 'medium' | 'high' = 'low';

    if (missingRate > 0.5) {
      severity = 'high';
    } else if (missingRate > 0.2) {
      severity = 'medium';
    }

    const recommendations: string[] = [];
    if (missingRate > 0.1) {
      recommendations.push('Consider data validation or cleaning');
    }
    if (missingRate > 0.3) {
      recommendations.push('High missing data rate - investigate data source');
    }

    return {
      missingCount,
      missingRate,
      missingCells,
      severity,
      recommendations,
    };
  }

  /**
   * Convert column letter to number (A = 1, B = 2, etc.)
   */
  private columnLetterToNumber(letter: string): number {
    let result = 0;
    for (let i = 0; i < letter.length; i++) {
      result = result * 26 + (letter.charCodeAt(i) - 64);
    }
    return result;
  }

  /**
   * Calculate column statistics
   */
  public calculateColumnStatistics(
    worksheet: ExcelJS.Worksheet,
    column: number,
    startRow: number,
    endRow: number,
    headerRow?: number
  ): ColumnStatistics {
    const values: unknown[] = [];
    let nullCount = 0;
    const uniqueValues = new Set();

    // Get column name from header if available
    let columnName: string | undefined;
    if (headerRow) {
      const headerCell = worksheet.getCell(headerRow, column);
      columnName = this.getCellDisplayValue(headerCell)?.toString();
    }

    // Collect values
    for (let row = startRow; row <= endRow; row++) {
      const cell = worksheet.getCell(row, column);
      const value = this.getCellDisplayValue(cell);

      if (value === null || value === undefined || value === '') {
        nullCount++;
      } else {
        values.push(value);
        uniqueValues.add(value);
      }
    }

    // Determine data type
    const dataType = this.inferColumnDataType(values);

    // Calculate statistics for numeric data
    let min: number | undefined;
    let max: number | undefined;
    let mean: number | undefined;
    let median: number | undefined;
    let standardDeviation: number | undefined;

    if (dataType === 'number') {
      const numericValues = values
        .map(v => (typeof v === 'number' ? v : parseFloat(String(v))))
        .filter(v => !isNaN(v));

      if (numericValues.length > 0) {
        min = Math.min(...numericValues);
        max = Math.max(...numericValues);
        mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;

        // Calculate median
        const sorted = [...numericValues].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        median =
          sorted.length % 2 === 0
            ? ((sorted[mid - 1] || 0) + (sorted[mid] || 0)) / 2
            : sorted[mid] || 0;

        // Calculate standard deviation
        const meanValue = mean || 0;
        const variance =
          numericValues.reduce((sum, val) => sum + Math.pow(val - meanValue, 2), 0) /
          numericValues.length;
        standardDeviation = Math.sqrt(variance);
      }
    }

    // Find mode (most frequent value)
    const frequency = new Map();
    values.forEach(value => {
      frequency.set(value, (frequency.get(value) || 0) + 1);
    });

    let mode: unknown;
    let maxFreq = 0;
    frequency.forEach((freq, value) => {
      if (freq > maxFreq) {
        maxFreq = freq;
        mode = value;
      }
    });

    const result: ColumnStatistics = {
      column,
      dataType,
      uniqueValues: uniqueValues.size,
      nullCount,
    };

    if (columnName) {
      result.name = columnName;
    }
    if (min !== undefined) {
      result.min = min;
    }
    if (max !== undefined) {
      result.max = max;
    }
    if (mean !== undefined) {
      result.mean = mean;
    }
    if (median !== undefined) {
      result.median = median;
    }
    if (mode !== undefined) {
      result.mode = mode;
    }
    if (standardDeviation !== undefined) {
      result.standardDeviation = standardDeviation;
    }

    return result;
  }

  /**
   * Infer column data type from values
   */
  private inferColumnDataType(
    values: unknown[]
  ): 'number' | 'text' | 'date' | 'boolean' | 'formula' | 'mixed' {
    if (values.length === 0) return 'text';

    const types = new Set();

    values.forEach(value => {
      types.add(this.inferCellType(value));
    });

    if (types.size === 1) {
      return Array.from(types)[0] as 'number' | 'text' | 'date' | 'boolean';
    } else if (types.size === 2 && types.has('number') && types.has('text')) {
      // Check if text values are actually numbers
      const allNumeric = values.every(value => {
        if (typeof value === 'number') return true;
        if (typeof value === 'string') {
          return !isNaN(Number(value)) && !isNaN(parseFloat(value));
        }
        return false;
      });

      return allNumeric ? 'number' : 'mixed';
    }

    return 'mixed';
  }
}
