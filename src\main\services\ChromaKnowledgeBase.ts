import { ChromaClient, Collection } from 'chromadb';
import { app } from 'electron';
import * as path from 'path';
import { logger } from '../utils/logger';

export interface KnowledgeItem {
  id: string;
  content: string;
  metadata: Record<string, unknown>;
  embedding?: number[];
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeSearchResult {
  item: KnowledgeItem;
  score: number;
  relevance: number;
  snippet?: string;
}

export class ChromaKnowledgeBaseService {
  private readonly chromaClient: ChromaClient;
  private readonly collections: Map<string, Collection> = new Map();
  private isInitialized = false;

  constructor() {
    // Initialize ChromaDB in userData directory
    const chromaPath = path.join(app.getPath('userData'), 'chroma_db');
    this.chromaClient = new ChromaClient({
      path: chromaPath,
    });
  }

  async initialize(): Promise<void> {
    try {
      // Ensure ChromaDB is running and accessible
      await this.chromaClient.heartbeat();

      // Create default collections
      await this.ensureCollection('documents');
      await this.ensureCollection('knowledge_base');
      await this.ensureCollection('extracted_data');
      await this.ensureCollection('embeddings'); // Dedicated collection for embeddings

      this.isInitialized = true;
      logger.info('ChromaDB initialized successfully', {
        collections: Array.from(this.collections.keys()),
      });
    } catch (error) {
      logger.error('Failed to initialize ChromaDB', error);
      throw error;
    }
  }

  private async ensureCollection(name: string): Promise<Collection> {
    try {
      const collection = await this.chromaClient.getCollection({ name });
      this.collections.set(name, collection);
      return collection;
    } catch (error) {
      // Collection doesn't exist, create it
      logger.info(`Creating ChromaDB collection: ${name}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      const collection = await this.chromaClient.createCollection({
        name,
        metadata: {
          description: `AI Document Processor ${name} collection`,
          created_at: new Date().toISOString(),
        },
      });
      this.collections.set(name, collection);
      logger.info(`Created ChromaDB collection: ${name}`);
      return collection;
    }
  }

  async storeInformation(
    data: KnowledgeItem,
    collectionName: string = 'knowledge_base'
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const addData: {
        ids: string[];
        documents: string[];
        metadatas: Array<Record<string, string | number | boolean | null>>;
        embeddings?: number[][];
      } = {
        ids: [data.id],
        documents: [data.content],
        metadatas: [
          {
            ...data.metadata,
            tags: data.tags?.join(',') || '',
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
          },
        ],
      };

      if (data.embedding) {
        addData.embeddings = [data.embedding];
      }

      await collection.add(addData);

      logger.info('Stored knowledge item in ChromaDB', {
        id: data.id,
        collection: collectionName,
      });
    } catch (error) {
      logger.error('Failed to store knowledge item', { error, id: data.id });
      throw error;
    }
  }

  async semanticSearch(
    query: string,
    collectionName: string = 'knowledge_base',
    limit: number = 10,
    threshold: number = 0.7
  ): Promise<KnowledgeSearchResult[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const results = await collection.query({
        queryTexts: [query],
        nResults: limit,
        include: ['documents', 'metadatas', 'distances'],
      });

      const searchResults: KnowledgeSearchResult[] = [];

      if (results.documents?.[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance;

          // Filter by threshold
          if (similarity >= threshold) {
            const metadata = results.metadatas?.[0]?.[i] || {};

            searchResults.push({
              item: {
                id: results.ids?.[0]?.[i] || `result_${i}`,
                content: results.documents[0][i] || '',
                metadata,
                tags: typeof metadata.tags === 'string' ? metadata.tags.split(',') : [],
                createdAt:
                  typeof metadata.createdAt === 'string'
                    ? metadata.createdAt
                    : new Date().toISOString(),
                updatedAt:
                  typeof metadata.updatedAt === 'string'
                    ? metadata.updatedAt
                    : new Date().toISOString(),
              },
              score: distance,
              relevance: similarity,
              snippet: this.generateSnippet(results.documents[0][i] || '', query),
            });
          }
        }
      }

      logger.info('Semantic search completed', {
        query,
        collection: collectionName,
        results: searchResults.length,
      });

      return searchResults.sort((a, b) => b.relevance - a.relevance);
    } catch (error) {
      logger.error('Semantic search failed', { error, query });
      throw error;
    }
  }

  private generateSnippet(content: string, query: string, maxLength: number = 200): string {
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();

    // Find the best match position
    let bestPosition = 0;
    let bestScore = 0;

    for (let i = 0; i < content.length - maxLength; i += 50) {
      const snippet = contentLower.slice(i, i + maxLength);
      const score = queryWords.reduce((acc, word) => {
        return acc + (snippet.includes(word) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestPosition = i;
      }
    }

    const snippet = content.slice(bestPosition, bestPosition + maxLength);
    return bestPosition > 0 ? '...' + snippet + '...' : snippet + '...';
  }

  async deleteInformation(id: string, collectionName: string = 'knowledge_base'): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      await collection.delete({ ids: [id] });
      logger.info('Deleted knowledge item from ChromaDB', { id, collection: collectionName });
    } catch (error) {
      logger.error('Failed to delete knowledge item', { error, id });
      throw error;
    }
  }

  async getCollectionStats(collectionName: string): Promise<{ count: number; name: string }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} not found`);
    }

    try {
      const count = await collection.count();
      return { count, name: collectionName };
    } catch (error) {
      logger.error('Failed to get collection stats', { error, collection: collectionName });
      throw error;
    }
  }

  cleanup(): void {
    try {
      // Perform any necessary cleanup
      this.collections.clear();
      this.isInitialized = false;
      logger.info('ChromaDB service cleaned up');
    } catch (error) {
      logger.error('Error during ChromaDB cleanup', error);
    }
  }
}

// Export singleton instance
export const chromaKnowledgeBase = new ChromaKnowledgeBaseService();
