import ModelClient from '@azure-rest/ai-inference';
import OpenAI from 'openai';
import { logger } from '../utils/logger';
import { chromaKnowledgeBase } from './ChromaKnowledgeBase';

export interface EmbeddingRequest {
  text: string;
  model?: string;
  dimensions?: number;
}

export interface EmbeddingResponse {
  embedding: number[];
  dimensions: number;
  model: string;
  tokensUsed: number;
}

export interface AIReasoningRequest {
  context: string;
  query: string;
  model?: string;
  useKnowledgeBase?: boolean;
}

export interface AIReasoningResponse {
  response: string;
  confidence: number;
  reasoning: string;
  sources: string[];
  tokensUsed: number;
}

export class AIModelClient {
  private azureClient?: ReturnType<typeof ModelClient>;
  private openaiClient?: OpenAI;
  private readonly embeddingCache = new Map<string, EmbeddingResponse>();

  constructor() {
    this.initializeClients();
  }

  private initializeClients(): void {
    try {
      // Initialize Azure AI client
      if (process.env.AZURE_AI_ENDPOINT && process.env.AZURE_AI_KEY) {
        this.azureClient = ModelClient(process.env.AZURE_AI_ENDPOINT, {
          key: process.env.AZURE_AI_KEY,
        });
        logger.info('Azure AI client initialized');
      }

      // Initialize OpenAI client
      if (process.env.OPENAI_API_KEY) {
        this.openaiClient = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
        logger.info('OpenAI client initialized');
      }

      if (!this.azureClient && !this.openaiClient) {
        logger.warn('No AI clients initialized - check environment variables');
      }
    } catch (error) {
      logger.error('Failed to initialize AI clients', error);
    }
  }

  /**
   * Generate embeddings using Azure AI or OpenAI
   * CRITICAL: All embeddings MUST be stored in ChromaDB
   */
  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const cacheKey = `${request.text}_${request.model || 'default'}`;

    // Check cache first
    if (this.embeddingCache.has(cacheKey)) {
      const cached = this.embeddingCache.get(cacheKey);
      if (!cached) {
        throw new Error('Cache inconsistency detected');
      }
      logger.info('Retrieved embedding from cache', { textLength: request.text.length });
      return cached;
    }

    try {
      let response: EmbeddingResponse;

      // Try Azure AI first
      if (this.azureClient) {
        response = await this.generateAzureEmbeddings(request);
      }
      // Fallback to OpenAI
      else if (this.openaiClient) {
        response = await this.generateOpenAIEmbeddings(request);
      } else {
        throw new Error('No AI clients available for embedding generation');
      }

      // Cache the response
      this.embeddingCache.set(cacheKey, response);

      // Store embedding in ChromaDB only for persistent knowledge
      // Temporary embeddings are kept in cache only to avoid ChromaDB pollution
      const isPersistent = request.text.length > 100; // Only store substantial content
      await this.storeEmbeddingInKnowledgeBase(request.text, response.embedding, isPersistent);

      logger.info('Generated and stored embedding', {
        textLength: request.text.length,
        dimensions: response.dimensions,
        model: response.model,
      });

      return response;
    } catch (error) {
      logger.error('Failed to generate embeddings', { error, textLength: request.text.length });
      throw error;
    }
  }

  private generateAzureEmbeddings(_request: EmbeddingRequest): Promise<EmbeddingResponse> {
    // TODO: Fix Azure AI client implementation
    throw new Error('Azure AI embeddings not implemented yet');
  }

  private async generateOpenAIEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    const response = await this.openaiClient.embeddings.create({
      input: request.text,
      model: request.model || 'text-embedding-ada-002',
      ...(request.dimensions && { dimensions: request.dimensions }),
    });

    const firstEmbedding = response.data[0];
    if (!firstEmbedding?.embedding) {
      throw new Error('No embedding data received from OpenAI');
    }

    return {
      embedding: firstEmbedding.embedding,
      dimensions: firstEmbedding.embedding.length,
      model: response.model,
      tokensUsed: response.usage.total_tokens,
    };
  }

  /**
   * Store embeddings in ChromaDB for semantic search
   * Only stores embeddings for persistent knowledge, not temporary cache data
   */
  private async storeEmbeddingInKnowledgeBase(
    text: string,
    embedding: number[],
    isPersistent: boolean = false
  ): Promise<void> {
    // Only store persistent embeddings to avoid ChromaDB pollution
    if (!isPersistent) {
      return;
    }

    try {
      const knowledgeItem = {
        id: `embedding_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        content: text,
        metadata: {
          type: 'embedding',
          source: 'ai_model_client',
          dimensions: embedding.length,
          persistent: true,
        },
        embedding,
        tags: ['embedding', 'ai_generated', 'persistent'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Use dedicated embeddings collection
      await chromaKnowledgeBase.storeInformation(knowledgeItem, 'embeddings');
    } catch (error) {
      logger.error('Failed to store embedding in ChromaDB', error);
      // Don't throw - embedding generation succeeded, storage is secondary
    }
  }

  /**
   * Perform AI reasoning with optional knowledge base integration
   */
  async performReasoning(request: AIReasoningRequest): Promise<AIReasoningResponse> {
    try {
      let enhancedContext = request.context;
      let sources: string[] = [];

      // Enhance context with knowledge base if requested
      if (request.useKnowledgeBase !== false) {
        const knowledgeResults = await chromaKnowledgeBase.semanticSearch(
          request.query,
          'knowledge_base',
          5,
          0.7
        );

        if (knowledgeResults.length > 0) {
          const knowledgeContext = knowledgeResults.map(result => result.item.content).join('\n\n');

          enhancedContext = `${request.context}\n\nRelevant Knowledge:\n${knowledgeContext}`;
          sources = knowledgeResults.map(result => result.item.id);
        }
      }

      // Generate response using AI model
      let response: string;
      let tokensUsed = 0;

      if (this.openaiClient) {
        const completion = await this.openaiClient.chat.completions.create({
          model: request.model || 'gpt-4',
          messages: [
            {
              role: 'system',
              content:
                'You are an AI assistant helping with document processing and analysis. Provide accurate, helpful responses based on the given context.',
            },
            {
              role: 'user',
              content: `Context: ${enhancedContext}\n\nQuery: ${request.query}`,
            },
          ],
          temperature: 0.7,
          max_tokens: 1000,
        });

        response = completion.choices[0]?.message?.content || 'No response generated';
        tokensUsed = completion.usage?.total_tokens || 0;
      } else {
        throw new Error('No AI client available for reasoning');
      }

      const result: AIReasoningResponse = {
        response,
        confidence: 0.8, // TODO: Implement confidence scoring
        reasoning: 'Generated using AI model with knowledge base enhancement',
        sources,
        tokensUsed,
      };

      logger.info('AI reasoning completed', {
        queryLength: request.query.length,
        contextLength: enhancedContext.length,
        sourcesUsed: sources.length,
        tokensUsed,
      });

      return result;
    } catch (error) {
      logger.error('AI reasoning failed', { error, query: request.query });
      throw error;
    }
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.embeddingCache.clear();
    logger.info('AI model client cache cleared');
  }
}

// Export singleton instance
export const aiModelClient = new AIModelClient();
