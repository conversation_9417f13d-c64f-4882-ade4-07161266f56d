import * as fs from 'fs-extra';
import * as path from 'path';

export interface LogEntry {
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  source: 'main' | 'renderer' | 'preload' | 'chromium';
  message: string;
  data?: any;
  stack?: string;
}

export class Logger {
  private logDir: string;
  private currentLogFile: string;
  private maxLogSize = 10 * 1024 * 1024; // 10MB
  private maxLogFiles = 10;
  private logQueue: LogEntry[] = [];
  private isWriting = false;

  constructor() {
    // Create logs directory in project root for development access
    this.logDir = path.join(process.cwd(), 'logs');
    this.ensureLogDirectory();
    this.currentLogFile = this.generateLogFileName();
    this.setupProcessHandlers();
  }

  private ensureLogDirectory(): void {
    try {
      fs.ensureDirSync(this.logDir);
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  private generateLogFileName(): string {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0] || 'unknown-date';
    const timeStr = now.toTimeString().split(' ')[0]?.replace(/:/g, '-') || 'unknown-time';
    return path.join(this.logDir, `app-${dateStr}-${timeStr}.log`);
  }

  private setupProcessHandlers(): void {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      const options = error.stack ? { stack: error.stack } : undefined;
      this.error('Uncaught Exception', error.message, options);
      // Don't exit immediately, let the log write
      setTimeout(() => process.exit(1), 1000);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, _promise) => {
      this.error('Unhandled Promise Rejection', String(reason));
    });

    // Handle process warnings
    process.on('warning', (warning) => {
      this.warn('Process Warning', warning.message);
    });
  }

  private async writeLogEntry(entry: LogEntry): Promise<void> {
    const logLine = JSON.stringify(entry) + '\n';

    try {
      // Check if log file is too large
      if (fs.existsSync(this.currentLogFile)) {
        const stats = await fs.stat(this.currentLogFile);
        if (stats.size > this.maxLogSize) {
          await this.rotateLogFile();
        }
      }

      await fs.appendFile(this.currentLogFile, logLine);
    } catch (error) {
      console.error('Failed to write log entry:', error);
    }
  }

  private async rotateLogFile(): Promise<void> {
    try {
      // Clean up old log files
      await this.cleanupOldLogs();

      // Generate new log file name
      this.currentLogFile = this.generateLogFileName();

      this.info('Log file rotated', `New log file: ${path.basename(this.currentLogFile)}`);
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private async cleanupOldLogs(): Promise<void> {
    try {
      const files = await fs.readdir(this.logDir);
      const logFiles = files
        .filter(file => file.startsWith('app-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          stat: fs.statSync(path.join(this.logDir, file))
        }))
        .sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime());

      // Keep only the most recent log files
      const filesToDelete = logFiles.slice(this.maxLogFiles);

      for (const file of filesToDelete) {
        await fs.unlink(file.path);
      }
    } catch (error) {
      console.error('Failed to cleanup old logs:', error);
    }
  }

  private async processLogQueue(): Promise<void> {
    if (this.isWriting || this.logQueue.length === 0) {
      return;
    }

    this.isWriting = true;

    try {
      while (this.logQueue.length > 0) {
        const entry = this.logQueue.shift();
        if (entry) {
          await this.writeLogEntry(entry);
        }
      }
    } catch (error) {
      console.error('Error processing log queue:', error);
    } finally {
      this.isWriting = false;
    }
  }

  private log(level: LogEntry['level'], source: LogEntry['source'], message: string, data?: any, stack?: string): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      source,
      message,
      data,
      ...(stack && { stack })
    };

    // Add to queue
    this.logQueue.push(entry);

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      const consoleMethod = level === 'error' || level === 'fatal' ? 'error' :
        level === 'warn' ? 'warn' : 'log';
      console[consoleMethod](`[${level.toUpperCase()}] [${source}] ${message}`, data || '');
    }

    // Process queue asynchronously
    setImmediate(() => this.processLogQueue());
  }

  // Public logging methods
  debug(message: string, data?: any): void {
    this.log('debug', 'main', message, data);
  }

  info(message: string, data?: any): void {
    this.log('info', 'main', message, data);
  }

  warn(message: string, data?: any): void {
    this.log('warn', 'main', message, data);
  }

  error(message: string, data?: any, options?: { stack?: string }): void {
    this.log('error', 'main', message, data, options?.stack);
  }

  fatal(message: string, data?: any, options?: { stack?: string }): void {
    this.log('fatal', 'main', message, data, options?.stack);
  }

  // Methods for logging from other processes
  logFromRenderer(level: LogEntry['level'], message: string, data?: any, stack?: string): void {
    this.log(level, 'renderer', message, data, stack);
  }

  logFromPreload(level: LogEntry['level'], message: string, data?: any, stack?: string): void {
    this.log(level, 'preload', message, data, stack);
  }

  logFromChromium(level: LogEntry['level'], message: string, data?: any): void {
    this.log(level, 'chromium', message, data);
  }

  // Get log directory for external access
  getLogDirectory(): string {
    return this.logDir;
  }

  // Get current log file path
  getCurrentLogFile(): string {
    return this.currentLogFile;
  }

  // Flush all pending logs (useful for shutdown)
  async flush(): Promise<void> {
    await this.processLogQueue();
  }
}

// Create singleton instance
export const logger = new Logger();