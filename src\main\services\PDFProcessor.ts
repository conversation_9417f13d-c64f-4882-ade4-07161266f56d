import * as pdfjsLib from 'pdfjs-dist';
import {
  DocumentType,
  ExtractedData,
  ExtractedDataType,
  ExtractionMethod,
  ProcessingOptions,
  ValidationResult,
} from '../../shared/types/Document';
import { PDFFormField } from '../../shared/types/PDF';
import { NodeCanvasLike, NodeCanvasRenderingContext2D } from '../../types/pdf';
import { logger } from '../utils/logger';
import { DocumentProcessingError, DocumentProcessor } from './DocumentProcessor';
import { OCREngine } from './OCREngine';
import { ImageProcessor } from './ImageProcessor';
// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');

// PDF.js annotation interface
interface PDFAnnotation {
  subtype: string;
  fieldName?: string;
  fieldType?: string;
  fieldValue?: string | boolean;
  required?: boolean;
  readOnly?: boolean;
  rect?: number[];
  options?: Array<{ displayValue?: string; exportValue?: string }>;
  buttonValue?: string;
}

// PDFKit document type
type PDFKitDocument = {
  info: Record<string, string>;
  fontSize: (size: number) => PDFKitDocument;
  font: (font: string) => PDFKitDocument;
  fillColor: (color: string) => PDFKitDocument;
  lineGap: (gap: number) => PDFKitDocument;
  text: (text: string, x?: number, y?: number, options?: Record<string, unknown>) => PDFKitDocument;
  rect: (x: number, y: number, width: number, height: number) => PDFKitDocument;
  stroke: () => PDFKitDocument;
  moveTo: (x: number, y: number) => PDFKitDocument;
  lineTo: (x: number, y: number) => PDFKitDocument;
  on: (event: string, callback: (chunk?: Buffer) => void) => PDFKitDocument;
  end: () => void;
  addPage: () => PDFKitDocument;
  save: () => PDFKitDocument;
  fillOpacity: (opacity: number) => PDFKitDocument;
  fill: () => PDFKitDocument;
  restore: () => PDFKitDocument;
  circle: (x: number, y: number, radius: number) => PDFKitDocument;
};

// pdf-lib document type
type PDFLibDocument = {
  getPages: () => PDFLibPage[];
  addPage: () => PDFLibPage;
  copyPages: (doc: PDFLibDocument, pageIndices: number[]) => Promise<PDFLibPage[]>;
  save: () => Promise<Uint8Array>;
  getForm: () => PDFLibForm;
  embedFont: (font: unknown) => Promise<unknown>;
};

type PDFLibPage = {
  getSize: () => { width: number; height: number };
  drawText: (text: string, options?: Record<string, unknown>) => void;
  drawRectangle: (options: Record<string, unknown>) => void;
  setRotation: (degrees: number) => void;
  node: { Annots?: unknown[] };
};

type PDFLibForm = {
  getFields: () => PDFLibField[];
  flatten: () => void;
};

type PDFLibField = {
  getName: () => string;
  constructor: { name: string };
  setText?: (text: string) => void;
  check?: () => void;
  uncheck?: () => void;
  select?: (option: string) => void;
};

export interface PDFTextWithCoordinates {
  text: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fontSize: number;
  fontFamily: string;
  pageNumber: number;
}

export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  creator?: string;
  producer?: string;
  creationDate?: Date;
  modificationDate?: Date;
  pdfVersion?: string;
  pageCount: number;
  isEncrypted: boolean;
  hasFormFields: boolean;
  permissions: {
    canPrint: boolean;
    canModify: boolean;
    canCopy: boolean;
    canAnnotate: boolean;
    canFillForms: boolean;
  };
}

export class PDFProcessor extends DocumentProcessor {
  private readonly ocrEngine: OCREngine;
  private readonly imageProcessor: ImageProcessor;

  constructor() {
    super('PDFProcessor', {
      supportedTypes: [DocumentType.PDF],
      canExtractText: true,
      canExtractImages: true,
      canExtractTables: true,
      canDetectFormFields: true,
      canPerformOCR: true, // Now supports OCR for scanned PDFs
      canPreserveFormatting: true,
      maxFileSize: 100 * 1024 * 1024, // 100MB
      supportedEncodings: ['utf-8', 'latin1'],
      requiresNetwork: false,
      processingTimeEstimate: 3, // Increased due to OCR processing
    });

    this.ocrEngine = new OCREngine({
      maxWorkers: 2,
      cacheEnabled: true,
      cacheTTL: 3600,
      defaultLanguage: 'eng',
      supportedLanguages: ['eng', 'spa', 'fra', 'deu'],
    });

    this.imageProcessor = new ImageProcessor();
  }

  /**
   * Initialize the PDF processor with OCR capabilities
   */
  public async initialize(): Promise<void> {
    await this.ocrEngine.initialize();
    logger.info('PDF Processor with OCR capabilities initialized');
  }

  /**
   * Extract text content from PDF with OCR fallback for scanned documents
   */
  public async extractText(buffer: Buffer, options?: ProcessingOptions): Promise<string> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      let fullText = '';
      let hasTextContent = false;

      // First, try to extract text normally
      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items.map(item => ('str' in item ? item.str : '')).join(' ');

        if (pageText.trim().length > 0) {
          hasTextContent = true;
          fullText += pageText + '\n';
        }
      }

      // If no text content found or OCR is explicitly requested, use OCR
      if (!hasTextContent || options?.performOCR) {
        logger.info('PDF appears to be scanned or OCR requested, using OCR extraction');

        try {
          const ocrText = await this.extractTextWithOCR(buffer, options);
          fullText = ocrText;
        } catch (ocrError) {
          logger.warn('OCR extraction failed, falling back to regular text extraction', {
            ocrError,
          });
          // If OCR fails, return whatever text we could extract normally
        }
      }

      void pdfDoc.destroy();
      return fullText.trim();
    } catch (error) {
      logger.error('Failed to extract text from PDF', { error });
      throw new DocumentProcessingError(
        'Failed to extract text from PDF',
        'PDF_TEXT_EXTRACTION_FAILED',
        '',
        'text_extraction'
      );
    }
  }

  /**
   * Extract text from PDF using OCR for scanned documents
   */
  private async extractTextWithOCR(buffer: Buffer, options?: ProcessingOptions): Promise<string> {
    try {
      // Convert PDF pages to images for OCR processing
      const pageImages = await this.convertAllPagesToImages(buffer, {
        scale: 2.0, // Higher scale for better OCR accuracy
        format: 'png',
      });

      let fullText = '';

      for (const pageImage of pageImages) {
        // Enhance image for better OCR
        const enhancedImage = await this.imageProcessor.enhanceForOCR(pageImage.imageBuffer);

        // Perform OCR on the enhanced image
        const ocrResult = await this.ocrEngine.extractTextFromImage(enhancedImage, {
          language: options?.ocrLanguage || 'eng',
          pageSegmentationMode: 3, // AUTO
          minimumConfidence: 60,
        });

        fullText += ocrResult.text + '\n';
      }

      return fullText.trim();
    } catch (error) {
      logger.error('OCR text extraction failed', { error });
      throw new DocumentProcessingError(
        'Failed to extract text using OCR',
        'PDF_OCR_EXTRACTION_FAILED',
        '',
        'ocr_extraction'
      );
    }
  }

  /**
   * Extract form fields using OCR for scanned forms
   */
  private async extractFormFieldsWithOCR(
    buffer: Buffer,
    _options?: ProcessingOptions
  ): Promise<ExtractedData[]> {
    try {
      const extractedData: ExtractedData[] = [];

      // Convert PDF pages to images for OCR processing
      const pageImages = await this.convertAllPagesToImages(buffer, {
        scale: 2.0,
        format: 'png',
      });

      for (const pageImage of pageImages) {
        // Enhance image for better OCR
        const enhancedImage = await this.imageProcessor.enhanceForOCR(pageImage.imageBuffer);

        // Extract form fields using OCR
        const formFields = await this.ocrEngine.extractFormFields(enhancedImage);

        if (formFields.length > 0) {
          extractedData.push({
            id: `ocr_form_fields_page_${pageImage.pageNumber}`,
            documentId: '',
            type: ExtractedDataType.FORM_FIELD,
            content: { fields: formFields },
            confidence: 0.8,
            extractionMethod: ExtractionMethod.OCR,
            createdAt: new Date(),
          });
        }

        // Extract tables using OCR
        const tableData = await this.ocrEngine.extractTabularData(enhancedImage);

        if (tableData.rows.length > 0) {
          extractedData.push({
            id: `ocr_table_page_${pageImage.pageNumber}`,
            documentId: '',
            type: ExtractedDataType.TABLE,
            content: tableData,
            confidence: tableData.confidence / 100,
            extractionMethod: ExtractionMethod.OCR,
            coordinates: tableData.bounds,
            createdAt: new Date(),
          });
        }
      }

      return extractedData;
    } catch (error) {
      logger.error('OCR form field extraction failed', { error });
      throw new DocumentProcessingError(
        'Failed to extract form fields using OCR',
        'PDF_OCR_FORM_EXTRACTION_FAILED',
        '',
        'ocr_form_extraction'
      );
    }
  }

  /**
   * Extract text with coordinate information
   */
  public async extractTextWithCoordinates(buffer: Buffer): Promise<PDFTextWithCoordinates[]> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const textWithCoordinates: PDFTextWithCoordinates[] = [];

      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const textContent = await page.getTextContent();

        textContent.items.forEach(item => {
          if ('str' in item && item.str?.trim()) {
            textWithCoordinates.push({
              text: item.str,
              x: item.transform[4],
              y: item.transform[5],
              width: item.width,
              height: item.height,
              fontSize: item.transform[0],
              fontFamily: item.fontName || 'Unknown',
              pageNumber: pageNum,
            });
          }
        });
      }

      void pdfDoc.destroy();
      return textWithCoordinates;
    } catch (error) {
      logger.error('Failed to extract text with coordinates from PDF', { error });
      throw new DocumentProcessingError(
        'Failed to extract text with coordinates from PDF',
        'PDF_COORDINATE_EXTRACTION_FAILED',
        '',
        'coordinate_extraction'
      );
    }
  }

  /**
   * Extract structured data from PDF
   */
  public async extractStructuredData(
    buffer: Buffer,
    options: ProcessingOptions
  ): Promise<ExtractedData[]> {
    try {
      const extractedData: ExtractedData[] = [];
      const pdfDoc = await this.loadPDFDocument(buffer);

      // Extract metadata
      const metadata = await this.extractMetadata(pdfDoc);
      extractedData.push({
        id: 'metadata',
        documentId: '', // Will be set by caller
        type: ExtractedDataType.TEXT, // Metadata as text content
        content: JSON.stringify(metadata),
        confidence: 1.0,
        extractionMethod: ExtractionMethod.PDF_PARSER,
        createdAt: new Date(),
      });

      // Extract form fields if requested
      if (options.detectFormFields) {
        const formFields = await this.extractFormFields(pdfDoc);
        if (formFields.length > 0) {
          extractedData.push({
            id: 'form_fields',
            documentId: '', // Will be set by caller
            type: ExtractedDataType.FORM_FIELD,
            content: JSON.stringify(formFields),
            confidence: 1.0,
            extractionMethod: ExtractionMethod.PDF_PARSER,
            createdAt: new Date(),
          });
        }

        // If OCR is enabled, also try OCR-based form field extraction
        if (options.performOCR) {
          try {
            const ocrExtractedData = await this.extractFormFieldsWithOCR(buffer, options);
            extractedData.push(...ocrExtractedData);
          } catch (ocrError) {
            logger.warn('OCR form field extraction failed', { ocrError });
          }
        }
      }

      // Extract text with coordinates if preserving formatting
      if (options.preserveFormatting) {
        const textWithCoords = await this.extractTextWithCoordinates(buffer);
        extractedData.push({
          id: 'text_coordinates',
          documentId: '', // Will be set by caller
          type: ExtractedDataType.TEXT,
          content: JSON.stringify(textWithCoords),
          confidence: 1.0,
          extractionMethod: ExtractionMethod.PDF_PARSER,
          createdAt: new Date(),
        });
      }

      void pdfDoc.destroy();
      return extractedData;
    } catch (error) {
      logger.error('Failed to extract structured data from PDF', { error });
      throw new DocumentProcessingError(
        'Failed to extract structured data from PDF',
        'PDF_STRUCTURED_EXTRACTION_FAILED',
        '',
        'structured_extraction'
      );
    }
  }

  /**
   * Validate extracted data
   */
  public async validateExtraction(extractedData: ExtractedData[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Add a minimal async operation to satisfy the linter
    await Promise.resolve();

    for (const data of extractedData) {
      const result: ValidationResult = {
        fieldId: data.id,
        isValid: true,
        errors: [],
        warnings: [],
      };

      // Validate metadata
      if (data.type === ExtractedDataType.TEXT && data.id === 'metadata') {
        try {
          const metadata = JSON.parse(data.content as string) as PDFMetadata;
          if (!metadata.pageCount || metadata.pageCount <= 0) {
            result.errors.push({
              code: 'INVALID_PAGE_COUNT',
              message: 'Invalid page count in PDF metadata',
              severity: 'error',
            });
            result.isValid = false;
          }
        } catch (error) {
          logger.warn('Failed to parse metadata', { error });
          result.errors.push({
            code: 'INVALID_METADATA_FORMAT',
            message: 'Invalid metadata format',
            severity: 'error',
          });
          result.isValid = false;
        }
      }

      // Validate form fields
      if (data.type === ExtractedDataType.FORM_FIELD) {
        try {
          const formFields = JSON.parse(data.content as string) as PDFFormField[];
          formFields.forEach(field => {
            if (!field.name || field.name.trim() === '') {
              result.warnings.push({
                code: 'EMPTY_FIELD_NAME',
                message: 'Form field has empty name',
                suggestion: 'Provide a meaningful name for the form field',
              });
            }
          });
        } catch (error) {
          logger.warn('Failed to parse form fields', { error });
          result.errors.push({
            code: 'INVALID_FORM_FIELDS_FORMAT',
            message: 'Invalid form fields format',
            severity: 'error',
          });
          result.isValid = false;
        }
      }

      results.push(result);
    }

    return results;
  }

  /**
   * Load PDF document from buffer
   */
  private async loadPDFDocument(buffer: Buffer): Promise<pdfjsLib.PDFDocumentProxy> {
    try {
      const loadingTask = pdfjsLib.getDocument({
        data: new Uint8Array(buffer),
        useSystemFonts: true,
        disableFontFace: false,
        verbosity: 0, // Reduce console output
      });

      return await loadingTask.promise;
    } catch (error) {
      logger.error('Failed to load PDF document', { error });
      throw new DocumentProcessingError(
        'Failed to load PDF document',
        'PDF_LOAD_FAILED',
        '',
        'document_loading'
      );
    }
  }

  /**
   * Extract PDF metadata
   */
  private async extractMetadata(pdfDoc: pdfjsLib.PDFDocumentProxy): Promise<PDFMetadata> {
    try {
      const metadata = await pdfDoc.getMetadata();
      const info = metadata.info || {};

      const infoObj = info as Record<string, unknown>;
      const result: PDFMetadata = {
        pageCount: pdfDoc.numPages,
        isEncrypted: false, // PDF.js handles decryption automatically
        hasFormFields: false, // Will be determined by form field extraction
        permissions: {
          canPrint: true,
          canModify: true,
          canCopy: true,
          canAnnotate: true,
          canFillForms: true,
        },
      };

      // Add optional fields only if they exist
      if (infoObj.Title && typeof infoObj.Title === 'string') result.title = infoObj.Title;
      if (infoObj.Author && typeof infoObj.Author === 'string') result.author = infoObj.Author;
      if (infoObj.Subject && typeof infoObj.Subject === 'string') result.subject = infoObj.Subject;
      if (infoObj.Keywords && typeof infoObj.Keywords === 'string')
        result.keywords = infoObj.Keywords;
      if (infoObj.Creator && typeof infoObj.Creator === 'string') result.creator = infoObj.Creator;
      if (infoObj.Producer && typeof infoObj.Producer === 'string')
        result.producer = infoObj.Producer;
      if (infoObj.PDFFormatVersion && typeof infoObj.PDFFormatVersion === 'string')
        result.pdfVersion = infoObj.PDFFormatVersion;
      if (infoObj.CreationDate) result.creationDate = new Date(infoObj.CreationDate as string);
      if (infoObj.ModDate) result.modificationDate = new Date(infoObj.ModDate as string);

      return result;
    } catch (error) {
      logger.error('Failed to extract PDF metadata', { error });
      throw new DocumentProcessingError(
        'Failed to extract PDF metadata',
        'PDF_METADATA_EXTRACTION_FAILED',
        '',
        'metadata_extraction'
      );
    }
  }

  /**
   * Extract form fields from PDF
   */
  private async extractFormFields(pdfDoc: pdfjsLib.PDFDocumentProxy): Promise<PDFFormField[]> {
    try {
      const formFields: PDFFormField[] = [];

      // Note: PDF.js doesn't have direct form field access in the same way as pdf-lib
      // This is a simplified implementation that would need to be enhanced
      // with actual form field detection logic

      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);

        // Get annotations which may include form fields
        const annotations = await page.getAnnotations();

        annotations.forEach((annotation: PDFAnnotation, index: number) => {
          if (annotation.subtype === 'Widget') {
            const rect = annotation.rect || [0, 0, 0, 0];
            formFields.push({
              name: annotation.fieldName || `field_${pageNum}_${index}`,
              type: this.mapAnnotationTypeToFormFieldType(annotation.fieldType || ''),
              value: annotation.fieldValue || '',
              required: annotation.required || false,
              readOnly: annotation.readOnly || false,
              bounds: {
                x: rect[0] || 0,
                y: rect[1] || 0,
                width: (rect[2] || 0) - (rect[0] || 0),
                height: (rect[3] || 0) - (rect[1] || 0),
                pageNumber: pageNum,
              },
              ...(annotation.options && {
                options: annotation.options.map(opt => opt.displayValue || opt.exportValue || ''),
              }),
            });
          }
        });
      }

      return formFields;
    } catch (error) {
      logger.error('Failed to extract form fields from PDF', { error });
      return []; // Return empty array instead of throwing to allow processing to continue
    }
  }

  /**
   * Map PDF annotation field type to our form field type
   */
  private mapAnnotationTypeToFormFieldType(
    fieldType: string
  ): 'text' | 'checkbox' | 'radio' | 'select' | 'signature' {
    switch (fieldType?.toLowerCase()) {
      case 'tx':
        return 'text';
      case 'btn':
        return 'checkbox';
      case 'ch':
        return 'select';
      case 'sig':
        return 'signature';
      default:
        return 'text';
    }
  }

  /**
   * Get detailed form field information including validation rules
   */
  public async getFormFieldDetails(buffer: Buffer): Promise<PDFFormField[]> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const formFields: PDFFormField[] = [];

      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const annotations = await page.getAnnotations();

        for (const annotation of annotations) {
          if (annotation.subtype === 'Widget') {
            const field = this.createDetailedFormField(annotation, pageNum, page);
            if (field) {
              formFields.push(field);
            }
          }
        }
      }

      void pdfDoc.destroy();
      return formFields;
    } catch (error) {
      logger.error('Failed to get detailed form field information', { error });
      throw new DocumentProcessingError(
        'Failed to get detailed form field information',
        'PDF_FORM_FIELD_DETAILS_FAILED',
        '',
        'form_field_analysis'
      );
    }
  }

  /**
   * Create detailed form field from annotation
   */
  private createDetailedFormField(
    annotation: PDFAnnotation,
    pageNum: number,
    page: pdfjsLib.PDFPageProxy
  ): PDFFormField | null {
    try {
      const viewport = page.getViewport({ scale: 1.0 });
      const rect = annotation.rect || [0, 0, 0, 0];

      // Convert PDF coordinates to standard coordinates
      const bounds = {
        x: rect[0] || 0,
        y: (viewport.height || 0) - (rect[3] || 0), // Flip Y coordinate
        width: (rect[2] || 0) - (rect[0] || 0),
        height: (rect[3] || 0) - (rect[1] || 0),
        pageNumber: pageNum,
      };

      const field: PDFFormField = {
        name: annotation.fieldName || `field_${pageNum}_${Date.now()}`,
        type: this.mapAnnotationTypeToFormFieldType(annotation.fieldType || ''),
        value: this.extractFieldValue(annotation),
        required: annotation.required || false,
        readOnly: annotation.readOnly || false,
        bounds,
      };

      // Add options for select fields
      if (field.type === 'select' && annotation.options) {
        field.options = annotation.options.map(opt => opt.displayValue || opt.exportValue || '');
      }

      return field;
    } catch (error) {
      logger.warn('Failed to create detailed form field', { error, annotation });
      return null;
    }
  }

  /**
   * Extract field value from annotation
   */
  private extractFieldValue(annotation: PDFAnnotation): string | boolean {
    if (annotation.fieldType === 'Btn') {
      // Button/checkbox field
      return annotation.fieldValue === 'Yes' || annotation.fieldValue === true;
    }

    return annotation.fieldValue || annotation.buttonValue || '';
  }

  /**
   * Validate form field coordinates and properties
   */
  public validateFormFields(formFields: PDFFormField[]): {
    valid: PDFFormField[];
    invalid: Array<{ field: PDFFormField; errors: string[] }>;
  } {
    const valid: PDFFormField[] = [];
    const invalid: Array<{ field: PDFFormField; errors: string[] }> = [];

    for (const field of formFields) {
      const errors: string[] = [];

      // Validate field name
      if (!field.name || field.name.trim() === '') {
        errors.push('Field name is required');
      }

      // Validate bounds
      if (!field.bounds || field.bounds.width <= 0 || field.bounds.height <= 0) {
        errors.push('Invalid field bounds');
      }

      // Validate page number
      if (field.bounds && field.bounds.pageNumber < 1) {
        errors.push('Invalid page number');
      }

      // Type-specific validation
      if (field.type === 'select' && (!field.options || field.options.length === 0)) {
        errors.push('Select field must have options');
      }

      if (errors.length > 0) {
        invalid.push({ field, errors });
      } else {
        valid.push(field);
      }
    }

    return { valid, invalid };
  }

  /**
   * Extract form field coordinates for template creation
   */
  public async extractFormFieldCoordinates(buffer: Buffer): Promise<
    Array<{
      name: string;
      type: string;
      coordinates: {
        x: number;
        y: number;
        width: number;
        height: number;
        pageNumber: number;
      };
      properties: {
        required: boolean;
        readOnly: boolean;
        multiline?: boolean;
        maxLength?: number;
        options?: string[];
      };
    }>
  > {
    try {
      const formFields = await this.getFormFieldDetails(buffer);

      return formFields.map(field => ({
        name: field.name,
        type: field.type,
        coordinates: {
          x: field.bounds.x,
          y: field.bounds.y,
          width: field.bounds.width,
          height: field.bounds.height,
          pageNumber: field.bounds.pageNumber,
        },
        properties: {
          required: field.required,
          readOnly: field.readOnly,
          ...(field.options && { options: field.options }),
        },
      }));
    } catch (error) {
      logger.error('Failed to extract form field coordinates', { error });
      throw new DocumentProcessingError(
        'Failed to extract form field coordinates',
        'PDF_FORM_COORDINATES_FAILED',
        '',
        'coordinate_extraction'
      );
    }
  }

  /**
   * Detect form field types using heuristics
   */
  public async detectFormFieldTypes(buffer: Buffer): Promise<
    Array<{
      name: string;
      detectedType: string;
      confidence: number;
      reasoning: string;
    }>
  > {
    try {
      const formFields = await this.getFormFieldDetails(buffer);
      const detections: Array<{
        name: string;
        detectedType: string;
        confidence: number;
        reasoning: string;
      }> = [];

      for (const field of formFields) {
        const detection = this.analyzeFieldType(field);
        detections.push({
          name: field.name,
          detectedType: detection.type,
          confidence: detection.confidence,
          reasoning: detection.reasoning,
        });
      }

      return detections;
    } catch (error) {
      logger.error('Failed to detect form field types', { error });
      throw new DocumentProcessingError(
        'Failed to detect form field types',
        'PDF_FORM_TYPE_DETECTION_FAILED',
        '',
        'type_detection'
      );
    }
  }

  /**
   * Analyze field type using heuristics
   */
  private analyzeFieldType(field: PDFFormField): {
    type: string;
    confidence: number;
    reasoning: string;
  } {
    const name = field.name.toLowerCase();
    let confidence = 0.5;
    let reasoning = 'Based on field properties';

    // Name-based heuristics
    if (name.includes('email')) {
      return { type: 'email', confidence: 0.9, reasoning: 'Field name contains "email"' };
    }
    if (name.includes('phone') || name.includes('tel')) {
      return { type: 'phone', confidence: 0.9, reasoning: 'Field name contains phone/tel' };
    }
    if (name.includes('date')) {
      return { type: 'date', confidence: 0.9, reasoning: 'Field name contains "date"' };
    }
    if (name.includes('signature') || name.includes('sign')) {
      return { type: 'signature', confidence: 0.95, reasoning: 'Field name contains signature' };
    }

    // Type-based analysis
    if (field.type === 'checkbox') {
      confidence = 0.95;
      reasoning = 'Checkbox field type detected';
    } else if (field.type === 'select') {
      confidence = 0.95;
      reasoning = 'Select field type detected';
    }

    // Size-based heuristics
    if (field.bounds.height < 20 && field.bounds.width > 100) {
      confidence = Math.max(confidence, 0.7);
      reasoning += ', single-line text field dimensions';
    } else if (field.bounds.height > 50) {
      return {
        type: 'textarea',
        confidence: 0.8,
        reasoning: 'Large height suggests multi-line text',
      };
    }

    return { type: field.type, confidence, reasoning };
  }

  // PDF Generation and Modification Methods using PDFKit

  /**
   * Create a new PDF document
   */
  public createPDF(options?: {
    title?: string;
    author?: string;
    subject?: string;
    keywords?: string[];
    pageSize?: [number, number] | string;
    margins?: { top?: number; bottom?: number; left?: number; right?: number };
  }): PDFKitDocument {
    try {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const PDFDocument = require('pdfkit');
      const pdfOptions: Record<string, unknown> = {};

      if (options?.pageSize) {
        pdfOptions.size = options.pageSize;
      }

      if (options?.margins) {
        pdfOptions.margins = options.margins;
      }

      const doc = new PDFDocument(pdfOptions) as PDFKitDocument;

      // Set document metadata
      if (options?.title) doc.info.Title = options.title;
      if (options?.author) doc.info.Author = options.author;
      if (options?.subject) doc.info.Subject = options.subject;
      if (options?.keywords) doc.info.Keywords = options.keywords.join(', ');

      return doc;
    } catch (error) {
      logger.error('Failed to create PDF document', { error });
      throw new DocumentProcessingError(
        'Failed to create PDF document',
        'PDF_CREATION_FAILED',
        '',
        'pdf_creation'
      );
    }
  }

  /**
   * Add text to PDF document
   */
  public addTextToPDF(
    doc: PDFKitDocument,
    text: string,
    x: number,
    y: number,
    options?: {
      fontSize?: number;
      font?: string;
      color?: string;
      align?: 'left' | 'center' | 'right' | 'justify';
      width?: number;
      height?: number;
      lineGap?: number;
    }
  ): PDFKitDocument {
    try {
      // Set font if specified
      if (options?.font) {
        doc.font(options.font);
      }

      // Set font size if specified
      if (options?.fontSize) {
        doc.fontSize(options.fontSize);
      }

      // Set color if specified
      if (options?.color) {
        doc.fillColor(options.color);
      }

      // Set line gap if specified
      if (options?.lineGap) {
        doc.lineGap(options.lineGap);
      }

      // Add text with options
      const textOptions: Record<string, unknown> = {};
      if (options?.align) textOptions.align = options.align;
      if (options?.width) textOptions.width = options.width;
      if (options?.height) textOptions.height = options.height;

      doc.text(text, x, y, textOptions);

      return doc;
    } catch (error) {
      logger.error('Failed to add text to PDF', { error });
      throw new DocumentProcessingError(
        'Failed to add text to PDF',
        'PDF_TEXT_ADD_FAILED',
        '',
        'pdf_text_addition'
      );
    }
  }

  /**
   * Add form field to PDF document
   */
  public addFormFieldToPDF(
    doc: PDFKitDocument,
    field: {
      name: string;
      type: 'text' | 'checkbox' | 'radio' | 'select';
      x: number;
      y: number;
      width: number;
      height: number;
      value?: string | boolean;
      options?: string[];
      required?: boolean;
      readOnly?: boolean;
    }
  ): PDFKitDocument {
    try {
      // Draw field border
      doc.rect(field.x, field.y, field.width, field.height).stroke();

      // Add field label/placeholder
      if (field.type === 'text') {
        doc.fontSize(10).text(field.name, field.x + 2, field.y - 15);
        if (field.value && typeof field.value === 'string') {
          doc.text(field.value, field.x + 2, field.y + 2);
        }
      } else if (field.type === 'checkbox') {
        doc.fontSize(10).text(field.name, field.x + field.width + 5, field.y + 2);
        if (field.value === true) {
          // Draw checkmark
          doc
            .moveTo(field.x + 2, field.y + field.height / 2)
            .lineTo(field.x + field.width / 2, field.y + field.height - 2)
            .lineTo(field.x + field.width - 2, field.y + 2)
            .stroke();
        }
      }

      return doc;
    } catch (error) {
      logger.error('Failed to add form field to PDF', { error });
      throw new DocumentProcessingError(
        'Failed to add form field to PDF',
        'PDF_FORM_FIELD_ADD_FAILED',
        '',
        'pdf_form_field_addition'
      );
    }
  }

  /**
   * Fill PDF form with data
   */
  public async fillPDFForm(
    buffer: Buffer,
    formData: Record<string, string | boolean | number>
  ): Promise<Buffer> {
    try {
      // Extract form fields from the original PDF
      const formFields = await this.getFormFieldDetails(buffer);

      // Create a new PDF document
      const doc = this.createPDF();

      // Convert original PDF to images and add as background
      // Note: This is a simplified implementation
      // In a real implementation, you would use pdf-lib for better form filling

      // Add form fields with filled data
      for (const field of formFields) {
        const fieldValue = formData[field.name];
        if (fieldValue !== undefined) {
          // Only add supported field types
          if (field.type !== 'signature') {
            this.addFormFieldToPDF(doc, {
              name: field.name,
              type: field.type,
              x: field.bounds.x,
              y: field.bounds.y,
              width: field.bounds.width,
              height: field.bounds.height,
              value: typeof fieldValue === 'number' ? fieldValue.toString() : fieldValue,
              required: field.required,
              readOnly: field.readOnly,
            });
          }
        }
      }

      // Convert PDFKit document to buffer
      return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];
        doc.on('data', (chunk?: Buffer) => chunk && chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);
        doc.end();
      });
    } catch (error) {
      logger.error('Failed to fill PDF form', { error });
      throw new DocumentProcessingError(
        'Failed to fill PDF form',
        'PDF_FORM_FILL_FAILED',
        '',
        'pdf_form_filling'
      );
    }
  }

  /**
   * Add annotation to PDF
   */
  public addAnnotationToPDF(
    doc: PDFKitDocument,
    annotation: {
      type: 'highlight' | 'note' | 'stamp';
      x: number;
      y: number;
      width: number;
      height: number;
      text?: string;
      color?: string;
      opacity?: number;
    }
  ): PDFKitDocument {
    try {
      const color = annotation.color || '#FFFF00';
      const opacity = annotation.opacity || 0.5;

      if (annotation.type === 'highlight') {
        doc
          .save()
          .fillColor(color)
          .fillOpacity(opacity)
          .rect(annotation.x, annotation.y, annotation.width, annotation.height)
          .fill()
          .restore();
      } else if (annotation.type === 'note') {
        // Draw note icon
        doc
          .save()
          .fillColor(color)
          .circle(annotation.x + 10, annotation.y + 10, 8)
          .fill()
          .fillColor('black')
          .fontSize(8)
          .text('!', annotation.x + 7, annotation.y + 6)
          .restore();

        if (annotation.text) {
          doc.fontSize(10).text(annotation.text, annotation.x + 20, annotation.y);
        }
      } else if (annotation.type === 'stamp') {
        doc
          .save()
          .rect(annotation.x, annotation.y, annotation.width, annotation.height)
          .stroke()
          .fontSize(12)
          .text(annotation.text || 'STAMP', annotation.x + 5, annotation.y + 5)
          .restore();
      }

      return doc;
    } catch (error) {
      logger.error('Failed to add annotation to PDF', { error });
      throw new DocumentProcessingError(
        'Failed to add annotation to PDF',
        'PDF_ANNOTATION_ADD_FAILED',
        '',
        'pdf_annotation_addition'
      );
    }
  }

  /**
   * Merge multiple PDF documents
   */
  public async mergePDFs(buffers: Buffer[]): Promise<Buffer> {
    try {
      const doc = this.createPDF();

      // Note: This is a simplified implementation
      // In a real implementation, you would use pdf-lib for proper PDF merging
      // This example just creates a new document with text indicating the merge

      doc.fontSize(16).text('Merged PDF Document', 50, 50);
      doc.fontSize(12).text(`Contains ${buffers.length} merged documents`, 50, 80);

      for (let i = 0; i < buffers.length; i++) {
        doc.addPage();
        doc.fontSize(14).text(`Content from Document ${i + 1}`, 50, 50);
        // Here you would extract and add content from each PDF
      }

      return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];
        doc.on('data', (chunk?: Buffer) => chunk && chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);
        doc.end();
      });
    } catch (error) {
      logger.error('Failed to merge PDFs', { error });
      throw new DocumentProcessingError(
        'Failed to merge PDFs',
        'PDF_MERGE_FAILED',
        '',
        'pdf_merging'
      );
    }
  }

  /**
   * Split PDF into separate pages
   */
  public async splitPDF(buffer: Buffer): Promise<Buffer[]> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const pageBuffers: Buffer[] = [];

      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const doc = this.createPDF();
        doc.fontSize(14).text(`Page ${pageNum} from original document`, 50, 50);

        // Note: This is a simplified implementation
        // In a real implementation, you would extract the actual page content

        const pageBuffer = await new Promise<Buffer>((resolve, reject) => {
          const chunks: Buffer[] = [];
          doc.on('data', (chunk?: Buffer) => chunk && chunks.push(chunk));
          doc.on('end', () => resolve(Buffer.concat(chunks)));
          doc.on('error', reject);
          doc.end();
        });

        pageBuffers.push(pageBuffer);
      }

      void pdfDoc.destroy();
      return pageBuffers;
    } catch (error) {
      logger.error('Failed to split PDF', { error });
      throw new DocumentProcessingError(
        'Failed to split PDF',
        'PDF_SPLIT_FAILED',
        '',
        'pdf_splitting'
      );
    }
  }

  // Advanced PDF Operations using pdf-lib

  /**
   * Load PDF document using pdf-lib for advanced operations
   */
  private async loadPDFLibDocument(buffer: Buffer): Promise<PDFLibDocument> {
    try {
      const { PDFDocument } = await import('pdf-lib');
      return (await PDFDocument.load(buffer)) as unknown as PDFLibDocument;
    } catch (error) {
      logger.error('Failed to load PDF with pdf-lib', { error });
      throw new DocumentProcessingError(
        'Failed to load PDF with pdf-lib',
        'PDF_LIB_LOAD_FAILED',
        '',
        'pdf_lib_loading'
      );
    }
  }

  /**
   * Extract annotations from PDF using pdf-lib
   */
  public async extractAnnotations(buffer: Buffer): Promise<
    Array<{
      type: string;
      content: string;
      page: number;
      bounds: { x: number; y: number; width: number; height: number };
      author?: string;
      creationDate?: Date;
      modificationDate?: Date;
    }>
  > {
    try {
      const pdfDoc = await this.loadPDFLibDocument(buffer);
      const annotations: Array<{
        type: string;
        content: string;
        page: number;
        bounds: { x: number; y: number; width: number; height: number };
        author?: string;
        creationDate?: Date;
        modificationDate?: Date;
      }> = [];

      const pages = pdfDoc.getPages();

      for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        const page = pages[pageIndex];
        if (!page) continue;
        const pageAnnotations = page.node.Annots;

        if (pageAnnotations) {
          // Note: This is a simplified implementation
          // In a real implementation, you would parse the annotation objects
          annotations.push({
            type: 'text',
            content: 'Annotation found on page',
            page: pageIndex + 1,
            bounds: { x: 0, y: 0, width: 100, height: 20 },
            author: 'Unknown',
            creationDate: new Date(),
          });
        }
      }

      return annotations;
    } catch (error) {
      logger.error('Failed to extract annotations', { error });
      throw new DocumentProcessingError(
        'Failed to extract annotations',
        'PDF_ANNOTATION_EXTRACTION_FAILED',
        '',
        'annotation_extraction'
      );
    }
  }

  /**
   * Add annotation to PDF using pdf-lib
   */
  public async addAdvancedAnnotation(
    buffer: Buffer,
    annotation: {
      type: 'text' | 'highlight' | 'stamp' | 'freetext';
      page: number;
      x: number;
      y: number;
      width: number;
      height: number;
      content: string;
      color?: { r: number; g: number; b: number };
      author?: string;
    }
  ): Promise<Buffer> {
    try {
      const pdfDoc = await this.loadPDFLibDocument(buffer);
      const { rgb } = await import('pdf-lib');

      const pages = pdfDoc.getPages();
      if (annotation.page < 1 || annotation.page > pages.length) {
        throw new Error(`Invalid page number: ${annotation.page}`);
      }

      const page = pages[annotation.page - 1];
      if (!page) {
        throw new Error(`Page ${annotation.page} not found`);
      }

      const color = annotation.color
        ? rgb(annotation.color.r / 255, annotation.color.g / 255, annotation.color.b / 255)
        : rgb(1, 1, 0); // Default yellow

      if (annotation.type === 'text' || annotation.type === 'freetext') {
        // Add text annotation
        const { StandardFonts } = await import('pdf-lib');
        const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

        page.drawText(annotation.content, {
          x: annotation.x,
          y: annotation.y,
          size: 12,
          font,
          color,
        });
      } else if (annotation.type === 'highlight') {
        // Add highlight annotation
        page.drawRectangle({
          x: annotation.x,
          y: annotation.y,
          width: annotation.width,
          height: annotation.height,
          color,
          opacity: 0.3,
        });
      } else if (annotation.type === 'stamp') {
        // Add stamp annotation
        page.drawRectangle({
          x: annotation.x,
          y: annotation.y,
          width: annotation.width,
          height: annotation.height,
          borderColor: color,
          borderWidth: 2,
        });

        const { StandardFonts } = await import('pdf-lib');
        const font = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

        page.drawText(annotation.content, {
          x: annotation.x + 5,
          y: annotation.y + annotation.height / 2,
          size: 10,
          font,
          color,
        });
      }

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      logger.error('Failed to add advanced annotation', { error });
      throw new DocumentProcessingError(
        'Failed to add advanced annotation',
        'PDF_ADVANCED_ANNOTATION_FAILED',
        '',
        'advanced_annotation'
      );
    }
  }

  /**
   * Fill PDF form using pdf-lib (more accurate than PDFKit approach)
   */
  public async fillPDFFormAdvanced(
    buffer: Buffer,
    formData: Record<string, string | boolean | number>
  ): Promise<Buffer> {
    try {
      const pdfDoc = await this.loadPDFLibDocument(buffer);
      const form = pdfDoc.getForm();

      // Get all form fields
      const fields = form.getFields();

      for (const field of fields) {
        const fieldName = field.getName();
        const fieldValue = formData[fieldName];

        if (fieldValue !== undefined) {
          try {
            if (field.constructor.name === 'PDFTextField' && field.setText) {
              field.setText(String(fieldValue));
            } else if (field.constructor.name === 'PDFCheckBox') {
              if (fieldValue && field.check) {
                field.check();
              } else if (!fieldValue && field.uncheck) {
                field.uncheck();
              }
            } else if (field.constructor.name === 'PDFRadioGroup' && field.select) {
              field.select(String(fieldValue));
            } else if (field.constructor.name === 'PDFDropdown' && field.select) {
              field.select(String(fieldValue));
            }
          } catch (fieldError) {
            logger.warn(`Failed to fill field ${fieldName}`, { fieldError });
          }
        }
      }

      // Flatten the form to prevent further editing
      form.flatten();

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      logger.error('Failed to fill PDF form with pdf-lib', { error });
      throw new DocumentProcessingError(
        'Failed to fill PDF form with pdf-lib',
        'PDF_FORM_FILL_ADVANCED_FAILED',
        '',
        'advanced_form_filling'
      );
    }
  }

  /**
   * Add digital signature placeholder to PDF
   */
  public async addSignaturePlaceholder(
    buffer: Buffer,
    signature: {
      page: number;
      x: number;
      y: number;
      width: number;
      height: number;
      signerName: string;
      reason?: string;
      location?: string;
    }
  ): Promise<Buffer> {
    try {
      const pdfDoc = await this.loadPDFLibDocument(buffer);
      const { rgb, StandardFonts } = await import('pdf-lib');

      const pages = pdfDoc.getPages();
      if (signature.page < 1 || signature.page > pages.length) {
        throw new Error(`Invalid page number: ${signature.page}`);
      }

      const page = pages[signature.page - 1];
      if (!page) {
        throw new Error(`Page ${signature.page} not found`);
      }

      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

      // Draw signature box
      page.drawRectangle({
        x: signature.x,
        y: signature.y,
        width: signature.width,
        height: signature.height,
        borderColor: rgb(0, 0, 0),
        borderWidth: 1,
      });

      // Add signature text
      page.drawText('Digital Signature', {
        x: signature.x + 5,
        y: signature.y + signature.height - 15,
        size: 10,
        font,
        color: rgb(0, 0, 0),
      });

      page.drawText(`Signer: ${signature.signerName}`, {
        x: signature.x + 5,
        y: signature.y + signature.height - 30,
        size: 8,
        font,
        color: rgb(0, 0, 0),
      });

      if (signature.reason) {
        page.drawText(`Reason: ${signature.reason}`, {
          x: signature.x + 5,
          y: signature.y + signature.height - 45,
          size: 8,
          font,
          color: rgb(0, 0, 0),
        });
      }

      if (signature.location) {
        page.drawText(`Location: ${signature.location}`, {
          x: signature.x + 5,
          y: signature.y + signature.height - 60,
          size: 8,
          font,
          color: rgb(0, 0, 0),
        });
      }

      page.drawText(`Date: ${new Date().toLocaleDateString()}`, {
        x: signature.x + 5,
        y: signature.y + 5,
        size: 8,
        font,
        color: rgb(0, 0, 0),
      });

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      logger.error('Failed to add signature placeholder', { error });
      throw new DocumentProcessingError(
        'Failed to add signature placeholder',
        'PDF_SIGNATURE_PLACEHOLDER_FAILED',
        '',
        'signature_placeholder'
      );
    }
  }

  /**
   * Merge PDFs using pdf-lib (more accurate than PDFKit approach)
   */
  public async mergePDFsAdvanced(buffers: Buffer[]): Promise<Buffer> {
    try {
      const { PDFDocument } = await import('pdf-lib');
      const mergedPdf = await PDFDocument.create();

      for (const buffer of buffers) {
        const pdf = await PDFDocument.load(buffer);
        const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        copiedPages.forEach(page => mergedPdf.addPage(page));
      }

      return Buffer.from(await mergedPdf.save());
    } catch (error) {
      logger.error('Failed to merge PDFs with pdf-lib', { error });
      throw new DocumentProcessingError(
        'Failed to merge PDFs with pdf-lib',
        'PDF_MERGE_ADVANCED_FAILED',
        '',
        'advanced_merging'
      );
    }
  }

  /**
   * Extract specific pages from PDF using pdf-lib
   */
  public async extractPages(buffer: Buffer, pageNumbers: number[]): Promise<Buffer> {
    try {
      const { PDFDocument } = await import('pdf-lib');
      const sourcePdf = await PDFDocument.load(buffer);
      const newPdf = await PDFDocument.create();

      // Validate page numbers
      const totalPages = sourcePdf.getPageCount();
      const validPageNumbers = pageNumbers.filter(num => num >= 1 && num <= totalPages);

      if (validPageNumbers.length === 0) {
        throw new Error('No valid page numbers provided');
      }

      // Copy specified pages
      const pageIndices = validPageNumbers.map(num => num - 1);
      const copiedPages = await newPdf.copyPages(sourcePdf, pageIndices);
      copiedPages.forEach(page => newPdf.addPage(page));

      return Buffer.from(await newPdf.save());
    } catch (error) {
      logger.error('Failed to extract pages', { error });
      throw new DocumentProcessingError(
        'Failed to extract pages',
        'PDF_PAGE_EXTRACTION_FAILED',
        '',
        'page_extraction'
      );
    }
  }

  /**
   * Rotate pages in PDF using pdf-lib
   */
  public async rotatePages(
    buffer: Buffer,
    rotations: Array<{ page: number; degrees: 0 | 90 | 180 | 270 }>
  ): Promise<Buffer> {
    try {
      const pdfDoc = await this.loadPDFLibDocument(buffer);
      const pages = pdfDoc.getPages();

      for (const rotation of rotations) {
        if (rotation.page >= 1 && rotation.page <= pages.length) {
          const page = pages[rotation.page - 1];
          if (page) {
            page.setRotation(rotation.degrees);
          }
        }
      }

      return Buffer.from(await pdfDoc.save());
    } catch (error) {
      logger.error('Failed to rotate pages', { error });
      throw new DocumentProcessingError(
        'Failed to rotate pages',
        'PDF_PAGE_ROTATION_FAILED',
        '',
        'page_rotation'
      );
    }
  }

  // PDF to Image Conversion using PDF.js

  /**
   * Convert PDF page to image using PDF.js canvas rendering
   */
  public async convertPageToImage(
    buffer: Buffer,
    pageNumber: number,
    options?: {
      scale?: number;
      format?: 'png' | 'jpeg';
      quality?: number;
      backgroundColor?: string;
    }
  ): Promise<Buffer> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);

      if (pageNumber < 1 || pageNumber > pdfDoc.numPages) {
        throw new Error(`Invalid page number: ${pageNumber}. PDF has ${pdfDoc.numPages} pages.`);
      }

      const page = await pdfDoc.getPage(pageNumber);
      const scale = options?.scale || 2.0; // Higher scale for better quality
      const viewport = page.getViewport({ scale });

      // Create canvas
      const canvas = this.createCanvas(viewport.width, viewport.height);
      const context = canvas.getContext('2d');

      // Set background color if specified
      if (options?.backgroundColor) {
        context.fillStyle = options.backgroundColor;
        context.fillRect(0, 0, viewport.width, viewport.height);
      }

      // Render PDF page to canvas
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
        canvas: canvas,
      };

      await page.render(renderContext).promise;

      // Convert canvas to buffer
      const format = options?.format || 'png';
      const quality = options?.quality || 0.9;

      if (format === 'jpeg') {
        return Buffer.from(canvas.toBuffer('image/jpeg', quality));
      } else {
        return Buffer.from(canvas.toBuffer('image/png'));
      }
    } catch (error) {
      logger.error('Failed to convert PDF page to image', { error, pageNumber });
      throw new DocumentProcessingError(
        'Failed to convert PDF page to image',
        'PDF_PAGE_TO_IMAGE_FAILED',
        '',
        'page_to_image_conversion'
      );
    }
  }

  /**
   * Convert all PDF pages to images
   */
  public async convertAllPagesToImages(
    buffer: Buffer,
    options?: {
      scale?: number;
      format?: 'png' | 'jpeg';
      quality?: number;
      backgroundColor?: string;
      maxPages?: number;
    }
  ): Promise<Array<{ pageNumber: number; imageBuffer: Buffer; width: number; height: number }>> {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const maxPages = options?.maxPages || pdfDoc.numPages;
      const pagesToProcess = Math.min(maxPages, pdfDoc.numPages);

      const images: Array<{
        pageNumber: number;
        imageBuffer: Buffer;
        width: number;
        height: number;
      }> = [];

      for (let pageNum = 1; pageNum <= pagesToProcess; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const scale = options?.scale || 2.0;
        const viewport = page.getViewport({ scale });

        const canvas = this.createCanvas(viewport.width, viewport.height);
        const context = canvas.getContext('2d');

        // Set background color if specified
        if (options?.backgroundColor) {
          context.fillStyle = options.backgroundColor;
          context.fillRect(0, 0, viewport.width, viewport.height);
        }

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
          canvas: canvas,
        };

        await page.render(renderContext).promise;

        // Convert to buffer
        const format = options?.format || 'png';
        const quality = options?.quality || 0.9;

        let imageBuffer: Buffer;
        if (format === 'jpeg') {
          imageBuffer = Buffer.from(canvas.toBuffer('image/jpeg', quality));
        } else {
          imageBuffer = Buffer.from(canvas.toBuffer('image/png'));
        }

        images.push({
          pageNumber: pageNum,
          imageBuffer,
          width: viewport.width,
          height: viewport.height,
        });
      }

      void pdfDoc.destroy();
      return images;
    } catch (error) {
      logger.error('Failed to convert PDF pages to images', { error });
      throw new DocumentProcessingError(
        'Failed to convert PDF pages to images',
        'PDF_PAGES_TO_IMAGES_FAILED',
        '',
        'pages_to_images_conversion'
      );
    }
  }

  /**
   * Generate thumbnail for PDF page
   */
  public async generateThumbnail(
    buffer: Buffer,
    pageNumber: number = 1,
    options?: {
      maxWidth?: number;
      maxHeight?: number;
      format?: 'png' | 'jpeg';
      quality?: number;
    }
  ): Promise<Buffer> {
    try {
      const maxWidth = options?.maxWidth || 200;
      const maxHeight = options?.maxHeight || 200;

      const pdfDoc = await this.loadPDFDocument(buffer);

      if (pageNumber < 1 || pageNumber > pdfDoc.numPages) {
        throw new Error(`Invalid page number: ${pageNumber}. PDF has ${pdfDoc.numPages} pages.`);
      }

      const page = await pdfDoc.getPage(pageNumber);
      const viewport = page.getViewport({ scale: 1.0 });

      // Calculate scale to fit within thumbnail dimensions
      const scaleX = maxWidth / viewport.width;
      const scaleY = maxHeight / viewport.height;
      const scale = Math.min(scaleX, scaleY);

      const thumbnailViewport = page.getViewport({ scale });
      const canvas = this.createCanvas(thumbnailViewport.width, thumbnailViewport.height);
      const context = canvas.getContext('2d');

      // White background for thumbnails
      context.fillStyle = 'white';
      context.fillRect(0, 0, thumbnailViewport.width, thumbnailViewport.height);

      const renderContext = {
        canvasContext: context,
        viewport: thumbnailViewport,
        canvas: canvas,
      };

      await page.render(renderContext).promise;

      // Convert to buffer
      const format = options?.format || 'jpeg'; // JPEG is better for thumbnails
      const quality = options?.quality || 0.8;

      void pdfDoc.destroy();

      if (format === 'jpeg') {
        return Buffer.from(canvas.toBuffer('image/jpeg', quality));
      } else {
        return Buffer.from(canvas.toBuffer('image/png'));
      }
    } catch (error) {
      logger.error('Failed to generate PDF thumbnail', { error, pageNumber });
      throw new DocumentProcessingError(
        'Failed to generate PDF thumbnail',
        'PDF_THUMBNAIL_GENERATION_FAILED',
        '',
        'thumbnail_generation'
      );
    }
  }

  /**
   * Extract images from PDF for OCR processing
   */
  public async extractImagesForOCR(
    buffer: Buffer,
    options?: {
      scale?: number;
      backgroundColor?: string;
      pageRange?: { start: number; end: number };
    }
  ): Promise<
    Array<{
      pageNumber: number;
      imageBuffer: Buffer;
      width: number;
      height: number;
      textRegions?: Array<{ x: number; y: number; width: number; height: number }>;
    }>
  > {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const startPage = options?.pageRange?.start || 1;
      const endPage = options?.pageRange?.end || pdfDoc.numPages;

      const images: Array<{
        pageNumber: number;
        imageBuffer: Buffer;
        width: number;
        height: number;
        textRegions?: Array<{ x: number; y: number; width: number; height: number }>;
      }> = [];

      for (let pageNum = startPage; pageNum <= Math.min(endPage, pdfDoc.numPages); pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const scale = options?.scale || 3.0; // Higher scale for OCR accuracy
        const viewport = page.getViewport({ scale });

        const canvas = this.createCanvas(viewport.width, viewport.height);
        const context = canvas.getContext('2d');

        // Set background color (white is best for OCR)
        const backgroundColor = options?.backgroundColor || 'white';
        context.fillStyle = backgroundColor;
        context.fillRect(0, 0, viewport.width, viewport.height);

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
          canvas: canvas,
        };

        await page.render(renderContext).promise;

        // Extract text regions for OCR optimization
        const textContent = await page.getTextContent();
        const textRegions: Array<{ x: number; y: number; width: number; height: number }> = [];

        textContent.items.forEach(item => {
          if ('str' in item && item.str?.trim()) {
            textRegions.push({
              x: item.transform[4] * scale,
              y: (viewport.height - item.transform[5]) * scale,
              width: item.width * scale,
              height: item.height * scale,
            });
          }
        });

        const imageBuffer = Buffer.from(canvas.toBuffer('image/png'));

        images.push({
          pageNumber: pageNum,
          imageBuffer,
          width: viewport.width,
          height: viewport.height,
          textRegions,
        });
      }

      void pdfDoc.destroy();
      return images;
    } catch (error) {
      logger.error('Failed to extract images for OCR', { error });
      throw new DocumentProcessingError(
        'Failed to extract images for OCR',
        'PDF_OCR_IMAGE_EXTRACTION_FAILED',
        '',
        'ocr_image_extraction'
      );
    }
  }

  /**
   * Create canvas for rendering (Node.js compatible)
   */
  private createCanvas(width: number, height: number): NodeCanvasLike {
    try {
      // Try to use node-canvas if available
      const { createCanvas } = require('canvas');
      return createCanvas(width, height);
    } catch (error) {
      // Fallback to a mock canvas for testing
      logger.warn('Canvas not available, using mock canvas', { error });
      return {
        width,
        height,
        getContext: () =>
          ({
            canvas: { width, height } as NodeCanvasLike,
            fillStyle: '',
            strokeStyle: '',
            lineWidth: 1,
            font: '',
            textAlign: 'start',
            textBaseline: 'alphabetic',
            globalAlpha: 1,
            globalCompositeOperation: 'source-over',
            fillRect: () => {},
            strokeRect: () => {},
            clearRect: () => {},
            fillText: () => {},
            strokeText: () => {},
            measureText: () => ({ width: 0 }),
            drawImage: () => {},
            beginPath: () => {},
            closePath: () => {},
            moveTo: () => {},
            lineTo: () => {},
            arc: () => {},
            arcTo: () => {},
            bezierCurveTo: () => {},
            quadraticCurveTo: () => {},
            rect: () => {},
            fill: () => {},
            stroke: () => {},
            clip: () => {},
            save: () => {},
            restore: () => {},
            scale: () => {},
            rotate: () => {},
            translate: () => {},
            transform: () => {},
            setTransform: () => {},
            resetTransform: () => {},
          }) as unknown as NodeCanvasRenderingContext2D,
        toBuffer: (_format?: string, _quality?: number) => Buffer.alloc(0),
      };
    }
  }

  /**
   * Get PDF page dimensions for image conversion planning
   */
  public async getPageDimensions(buffer: Buffer): Promise<
    Array<{
      pageNumber: number;
      width: number;
      height: number;
      rotation: number;
    }>
  > {
    try {
      const pdfDoc = await this.loadPDFDocument(buffer);
      const dimensions: Array<{
        pageNumber: number;
        width: number;
        height: number;
        rotation: number;
      }> = [];

      for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);
        const viewport = page.getViewport({ scale: 1.0 });

        dimensions.push({
          pageNumber: pageNum,
          width: viewport.width,
          height: viewport.height,
          rotation: viewport.rotation,
        });
      }

      void pdfDoc.destroy();
      return dimensions;
    } catch (error) {
      logger.error('Failed to get page dimensions', { error });
      throw new DocumentProcessingError(
        'Failed to get page dimensions',
        'PDF_PAGE_DIMENSIONS_FAILED',
        '',
        'page_dimensions'
      );
    }
  }

  /**
   * Cleanup OCR resources
   */
  public async cleanup(): Promise<void> {
    await this.ocrEngine.cleanup();
    logger.info('PDF Processor OCR resources cleaned up');
  }
}
