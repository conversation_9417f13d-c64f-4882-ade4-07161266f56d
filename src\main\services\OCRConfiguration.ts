import { 
  PageSegmentationMode, 
  OCREngineMode,
  ImageProcessingOptions 
} from '../../shared/types/Document';

/**
 * OCR Engine Configuration
 */
export interface OCREngineConfiguration {
  // Worker Configuration
  maxWorkers: number;
  workerTimeout: number; // milliseconds
  
  // Caching Configuration
  cacheEnabled: boolean;
  cacheTTL: number; // seconds
  cacheMaxSize: number; // number of entries
  
  // Language Configuration
  defaultLanguage: string;
  supportedLanguages: string[];
  autoDetectLanguage: boolean;
  
  // Performance Configuration
  processingQuality: 'fast' | 'balanced' | 'accurate';
  enableParallelProcessing: boolean;
  maxConcurrentJobs: number;
  
  // OCR Engine Settings
  defaultPageSegmentationMode: PageSegmentationMode;
  defaultOCREngineMode: OCREngineMode;
  minimumConfidenceThreshold: number;
  
  // Image Enhancement Settings
  enableImageEnhancement: boolean;
  imageEnhancementOptions: ImageProcessingOptions;
  
  // Advanced Features
  enableTableDetection: boolean;
  enableFormFieldDetection: boolean;
  enableHandwritingRecognition: boolean;
  enableBarcodeDetection: boolean;
  
  // Error Handling
  retryAttempts: number;
  retryDelay: number; // milliseconds
  fallbackToLowerQuality: boolean;
  
  // Logging and Monitoring
  enableDetailedLogging: boolean;
  enablePerformanceMetrics: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

/**
 * Document-specific OCR Options
 */
export interface DocumentOCROptions {
  // Language Settings
  language?: string;
  fallbackLanguages?: string[];
  
  // Processing Settings
  pageSegmentationMode?: PageSegmentationMode;
  ocrEngineMode?: OCREngineMode;
  minimumConfidence?: number;
  
  // Image Enhancement
  enhanceImage?: boolean;
  imageEnhancementLevel?: 'light' | 'medium' | 'aggressive';
  
  // Feature Detection
  detectTables?: boolean;
  detectFormFields?: boolean;
  detectHandwriting?: boolean;
  detectBarcodes?: boolean;
  
  // Output Options
  preserveFormatting?: boolean;
  includeCoordinates?: boolean;
  includeConfidenceScores?: boolean;
  
  // Performance Options
  useWorkerThread?: boolean;
  timeout?: number; // milliseconds
  priority?: 'low' | 'normal' | 'high';
}

/**
 * OCR Quality Presets
 */
export const OCR_QUALITY_PRESETS: Record<string, Partial<OCREngineConfiguration>> = {
  fast: {
    processingQuality: 'fast',
    maxWorkers: 1,
    enableImageEnhancement: false,
    enableTableDetection: false,
    enableFormFieldDetection: false,
    enableHandwritingRecognition: false,
    minimumConfidenceThreshold: 50,
    defaultPageSegmentationMode: PageSegmentationMode.AUTO,
    defaultOCREngineMode: OCREngineMode.TESSERACT_ONLY
  },
  
  balanced: {
    processingQuality: 'balanced',
    maxWorkers: 2,
    enableImageEnhancement: true,
    enableTableDetection: true,
    enableFormFieldDetection: true,
    enableHandwritingRecognition: false,
    minimumConfidenceThreshold: 60,
    defaultPageSegmentationMode: PageSegmentationMode.AUTO,
    defaultOCREngineMode: OCREngineMode.LSTM_ONLY
  },
  
  accurate: {
    processingQuality: 'accurate',
    maxWorkers: 3,
    enableImageEnhancement: true,
    enableTableDetection: true,
    enableFormFieldDetection: true,
    enableHandwritingRecognition: true,
    enableBarcodeDetection: true,
    minimumConfidenceThreshold: 70,
    defaultPageSegmentationMode: PageSegmentationMode.AUTO,
    defaultOCREngineMode: OCREngineMode.LSTM_ONLY
  }
};

/**
 * Document Type Specific OCR Configurations
 */
export const DOCUMENT_TYPE_OCR_CONFIGS: Record<string, DocumentOCROptions> = {
  'tax-form': {
    detectFormFields: true,
    detectTables: true,
    enhanceImage: true,
    imageEnhancementLevel: 'aggressive',
    minimumConfidence: 70,
    preserveFormatting: true,
    includeCoordinates: true
  },
  
  'invoice': {
    detectTables: true,
    detectFormFields: false,
    enhanceImage: true,
    imageEnhancementLevel: 'medium',
    minimumConfidence: 65,
    preserveFormatting: true
  },
  
  'receipt': {
    detectTables: false,
    detectFormFields: false,
    enhanceImage: true,
    imageEnhancementLevel: 'aggressive',
    minimumConfidence: 60,
    preserveFormatting: false
  },
  
  'contract': {
    detectFormFields: true,
    detectTables: false,
    enhanceImage: true,
    imageEnhancementLevel: 'medium',
    minimumConfidence: 75,
    preserveFormatting: true,
    detectHandwriting: true
  },
  
  'bank-statement': {
    detectTables: true,
    detectFormFields: false,
    enhanceImage: true,
    imageEnhancementLevel: 'medium',
    minimumConfidence: 70,
    preserveFormatting: true
  }
};

/**
 * Default OCR Configuration
 */
export const DEFAULT_OCR_CONFIG: OCREngineConfiguration = {
  // Worker Configuration
  maxWorkers: 3,
  workerTimeout: 60000, // 60 seconds
  
  // Caching Configuration
  cacheEnabled: true,
  cacheTTL: 3600, // 1 hour
  cacheMaxSize: 1000,
  
  // Language Configuration
  defaultLanguage: 'eng',
  supportedLanguages: ['eng', 'spa', 'fra', 'deu'],
  autoDetectLanguage: false,
  
  // Performance Configuration
  processingQuality: 'balanced',
  enableParallelProcessing: true,
  maxConcurrentJobs: 5,
  
  // OCR Engine Settings
  defaultPageSegmentationMode: PageSegmentationMode.AUTO,
  defaultOCREngineMode: OCREngineMode.LSTM_ONLY,
  minimumConfidenceThreshold: 60,
  
  // Image Enhancement Settings
  enableImageEnhancement: true,
  imageEnhancementOptions: {
    enhanceForOCR: true,
    upscaleRatio: 2,
    removeNoise: true,
    adjustContrast: true,
    adjustBrightness: false,
    sharpen: true,
    deskew: true,
    cropToContent: false,
    outputFormat: 'png',
    quality: 95,
    preserveMetadata: false
  },
  
  // Advanced Features
  enableTableDetection: true,
  enableFormFieldDetection: true,
  enableHandwritingRecognition: false,
  enableBarcodeDetection: false,
  
  // Error Handling
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
  fallbackToLowerQuality: true,
  
  // Logging and Monitoring
  enableDetailedLogging: false,
  enablePerformanceMetrics: true,
  logLevel: 'info'
};

/**
 * OCR Configuration Manager
 */
export class OCRConfigurationManager {
  private config: OCREngineConfiguration;
  
  constructor(initialConfig?: Partial<OCREngineConfiguration>) {
    this.config = { ...DEFAULT_OCR_CONFIG, ...initialConfig };
  }
  
  /**
   * Get current configuration
   */
  public getConfig(): OCREngineConfiguration {
    return { ...this.config };
  }
  
  /**
   * Update configuration
   */
  public updateConfig(updates: Partial<OCREngineConfiguration>): void {
    this.config = { ...this.config, ...updates };
  }
  
  /**
   * Apply quality preset
   */
  public applyQualityPreset(preset: 'fast' | 'balanced' | 'accurate'): void {
    const presetConfig = OCR_QUALITY_PRESETS[preset];
    if (presetConfig) {
      this.updateConfig(presetConfig);
    }
  }
  
  /**
   * Get document-specific OCR options
   */
  public getDocumentOCROptions(documentType: string): DocumentOCROptions {
    const baseOptions: DocumentOCROptions = {
      language: this.config.defaultLanguage,
      pageSegmentationMode: this.config.defaultPageSegmentationMode,
      ocrEngineMode: this.config.defaultOCREngineMode,
      minimumConfidence: this.config.minimumConfidenceThreshold,
      enhanceImage: this.config.enableImageEnhancement,
      detectTables: this.config.enableTableDetection,
      detectFormFields: this.config.enableFormFieldDetection,
      detectHandwriting: this.config.enableHandwritingRecognition,
      detectBarcodes: this.config.enableBarcodeDetection,
      useWorkerThread: this.config.enableParallelProcessing,
      timeout: this.config.workerTimeout
    };
    
    // Apply document-specific overrides
    const documentSpecificConfig = DOCUMENT_TYPE_OCR_CONFIGS[documentType];
    if (documentSpecificConfig) {
      return { ...baseOptions, ...documentSpecificConfig };
    }
    
    return baseOptions;
  }
  
  /**
   * Validate configuration
   */
  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (this.config.maxWorkers < 1 || this.config.maxWorkers > 10) {
      errors.push('maxWorkers must be between 1 and 10');
    }
    
    if (this.config.cacheTTL < 0) {
      errors.push('cacheTTL must be non-negative');
    }
    
    if (this.config.minimumConfidenceThreshold < 0 || this.config.minimumConfidenceThreshold > 100) {
      errors.push('minimumConfidenceThreshold must be between 0 and 100');
    }
    
    if (this.config.supportedLanguages.length === 0) {
      errors.push('supportedLanguages cannot be empty');
    }
    
    if (!this.config.supportedLanguages.includes(this.config.defaultLanguage)) {
      errors.push('defaultLanguage must be included in supportedLanguages');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Reset to default configuration
   */
  public resetToDefaults(): void {
    this.config = { ...DEFAULT_OCR_CONFIG };
  }
}

// Export singleton instance
export const ocrConfigManager = new OCRConfigurationManager();
