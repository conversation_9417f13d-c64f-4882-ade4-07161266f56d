import csv from 'csv-parser';
import { Readable } from 'stream';
import {
  DocumentType,
  ExtractedData,
  ExtractedDataType,
  ExtractionMethod,
  ProcessingOptions,
  TableRow,
  ValidationResult,
} from '../../shared/types/Document';
import { DocumentProcessor } from './DocumentProcessor';

/**
 * CSV processing configuration options
 */
export interface CSVProcessingOptions extends ProcessingOptions {
  delimiter?: string;
  quote?: string;
  escape?: string;
  encoding?: BufferEncoding;
  skipEmptyLines?: boolean;
  skipLinesWithError?: boolean;
  maxRows?: number;
  headers?: boolean | string[];
  strict?: boolean;
}

/**
 * CSV column metadata
 */
export interface CSVColumnMetadata {
  name: string;
  originalName: string;
  index: number;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'mixed';
  nullCount: number;
  uniqueCount: number;
  sampleValues: unknown[];
  hasNulls: boolean;
  isUnique: boolean;
}

/**
 * CSV parsing result
 */
export interface CSVParsingResult {
  rows: Record<string, unknown>[];
  headers: string[];
  metadata: {
    totalRows: number;
    totalColumns: number;
    delimiter: string;
    encoding: string;
    hasHeaders: boolean;
    columns: CSVColumnMetadata[];
    errors: CSVParsingError[];
    warnings: string[];
  };
}

/**
 * CSV parsing error
 */
export interface CSVParsingError {
  row: number;
  column?: string;
  message: string;
  rawLine?: string;
}

/**
 * CSV data validation result
 */
export interface CSVValidationResult {
  isValid: boolean;
  errors: CSVParsingError[];
  warnings: string[];
  duplicateRows: number[];
  emptyRows: number[];
  malformedRows: number[];
}

/**
 * Column statistics for summary calculations
 */
export interface ColumnStatistics {
  count: number;
  uniqueCount: number;
  nullCount: number;
  min?: number;
  max?: number;
  mean?: number;
  median?: number;
  sum?: number;
  standardDeviation?: number;
  mostCommon?: { value: unknown; count: number };
}

/**
 * Simple table interface for CSV data
 */
export interface Table {
  id: string;
  pageNumber: number;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
    pageNumber: number;
  };
  rows: TableRow[];
  headers: string[];
}

/**
 * CSV processor for parsing and extracting data from CSV files
 */
export class CSVProcessor extends DocumentProcessor {
  private readonly maxColumnsToProcess = 1000; // 1K columns limit
  private readonly sampleSize = 100; // Rows to sample for type inference

  constructor() {
    super('CSVProcessor', {
      supportedTypes: [DocumentType.CSV],
      canExtractText: true,
      canExtractImages: false,
      canExtractTables: true,
      canDetectFormFields: false,
      canPerformOCR: false,
      canPreserveFormatting: false,
      maxFileSize: 100 * 1024 * 1024, // 100MB
      supportedEncodings: ['utf-8', 'utf-16le', 'ascii', 'latin1'],
      requiresNetwork: false,
      processingTimeEstimate: 0.5, // seconds per MB
    });
  }

  /**
   * Validate extraction results
   */
  public validateExtraction(extractedData: ExtractedData[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Basic validation
    if (extractedData.length === 0) {
      results.push({
        fieldId: 'csv_extraction',
        isValid: false,
        errors: [
          {
            code: 'NO_DATA_EXTRACTED',
            message: 'No data extracted from CSV file',
            severity: 'error',
          },
        ],
        warnings: [],
      });
    }

    // Check for CSV metadata
    const hasMetadata = extractedData.some(d => d.id === 'csv_metadata');
    if (!hasMetadata) {
      results.push({
        fieldId: 'csv_metadata',
        isValid: false,
        errors: [],
        warnings: [
          {
            code: 'MISSING_METADATA',
            message: 'Missing CSV metadata',
          },
        ],
      });
    }

    // Check for table data
    const hasTable = extractedData.some(d => d.id === 'csv_table');
    if (!hasTable) {
      results.push({
        fieldId: 'csv_table',
        isValid: false,
        errors: [],
        warnings: [
          {
            code: 'MISSING_TABLE_DATA',
            message: 'Missing CSV table data',
          },
        ],
      });
    }

    // If no issues found, add success result
    if (results.length === 0) {
      results.push({
        fieldId: 'csv_extraction',
        isValid: true,
        errors: [],
        warnings: [],
      });
    }

    return Promise.resolve(results);
  }

  /**
   * Extract text content from CSV file
   */
  public async extractText(
    buffer: Buffer,
    options: Partial<CSVProcessingOptions> = {}
  ): Promise<string> {
    try {
      const parsingResult = await this.parseCSV(buffer, options);
      const textParts: string[] = [];

      // Add headers
      if (parsingResult.headers.length > 0) {
        textParts.push(parsingResult.headers.join('\t'));
      }

      // Add data rows
      parsingResult.rows.forEach(row => {
        const rowValues = parsingResult.headers.map(header => {
          const value = row[header];
          if (value === null || value === undefined) return '';
          if (typeof value === 'object') return JSON.stringify(value);
          if (typeof value === 'string') return value;
          if (typeof value === 'number' || typeof value === 'boolean') return String(value);
          return String(value);
        });
        textParts.push(rowValues.join('\t'));
      });

      return textParts.join('\n');
    } catch (error) {
      throw new Error(`Failed to extract text from CSV: ${error}`);
    }
  }

  /**
   * Extract structured data from CSV file
   */
  public async extractStructuredData(
    buffer: Buffer,
    options: Partial<ProcessingOptions> = {}
  ): Promise<ExtractedData[]> {
    try {
      const parsingResult = await this.parseCSV(buffer, options);
      const extractedData: ExtractedData[] = [];

      // Extract CSV metadata
      extractedData.push({
        id: 'csv_metadata',
        documentId: '', // Will be set by caller
        type: ExtractedDataType.TEXT,
        content: JSON.stringify(parsingResult.metadata),
        confidence: 1.0,
        extractionMethod: ExtractionMethod.CSV_PARSER,
        createdAt: new Date(),
      });

      // Extract table data
      const table = this.convertToTable(parsingResult);
      if (table) {
        extractedData.push({
          id: 'csv_table',
          documentId: '', // Will be set by caller
          type: ExtractedDataType.TABLE,
          content: table,
          confidence: this.calculateTableConfidence(parsingResult),
          extractionMethod: ExtractionMethod.CSV_PARSER,
          createdAt: new Date(),
        });
      }

      // Extract column statistics
      parsingResult.metadata.columns.forEach((column, index) => {
        extractedData.push({
          id: `column_stats_${index}`,
          documentId: '', // Will be set by caller
          type: ExtractedDataType.TEXT,
          content: JSON.stringify({
            columnName: column.name,
            dataType: column.dataType,
            nullCount: column.nullCount,
            uniqueCount: column.uniqueCount,
            isUnique: column.isUnique,
            sampleValues: column.sampleValues,
          }),
          confidence: 0.9,
          extractionMethod: ExtractionMethod.CSV_PARSER,
          createdAt: new Date(),
        });
      });

      return extractedData;
    } catch (error) {
      throw new Error(`Failed to extract structured data from CSV: ${error}`);
    }
  }

  /**
   * Parse CSV buffer into structured data
   */
  private async parseCSV(
    buffer: Buffer,
    options: Partial<CSVProcessingOptions> = {}
  ): Promise<CSVParsingResult> {
    // Detect encoding
    const encoding = options.encoding || this.detectEncoding(buffer);

    // Detect delimiter
    const delimiter = options.delimiter || this.detectDelimiter(buffer, encoding);

    // Convert buffer to string with detected encoding
    const csvText = buffer.toString(encoding);

    // Parse CSV
    return new Promise((resolve, reject) => {
      const rows: Record<string, unknown>[] = [];
      const errors: CSVParsingError[] = [];
      let headers: string[] = [];
      let rowCount = 0;

      const stream = Readable.from([csvText]);

      stream
        .pipe(
          csv({
            separator: delimiter,
            quote: options.quote || '"',
            escape: options.escape || '"',
            headers: options.headers !== false,
            strict: options.strict || false,
          })
        )
        .on('headers', (headerList: string[]) => {
          headers = this.normalizeHeaders(headerList);
        })
        .on('data', (row: Record<string, unknown>) => {
          if (options.maxRows && rowCount >= options.maxRows) {
            return;
          }

          try {
            const processedRow = this.processRow(row, headers);
            rows.push(processedRow);
            rowCount++;
          } catch (error) {
            errors.push({
              row: rowCount + 1,
              message: `Row processing error: ${error}`,
              rawLine: JSON.stringify(row),
            });

            if (!options.skipLinesWithError) {
              reject(new Error(`CSV parsing failed at row ${rowCount + 1}: ${error}`));
              return;
            }
          }
        })
        .on('error', (error: Error) => {
          reject(new Error(`CSV parsing error: ${error.message}`));
        })
        .on('end', () => {
          try {
            // Analyze columns
            const columns = this.analyzeColumns(rows, headers);

            const result: CSVParsingResult = {
              rows,
              headers,
              metadata: {
                totalRows: rows.length,
                totalColumns: headers.length,
                delimiter,
                encoding,
                hasHeaders: options.headers !== false,
                columns,
                errors,
                warnings: this.generateWarnings(rows, columns, errors),
              },
            };

            resolve(result);
          } catch (error) {
            reject(new Error(`CSV analysis failed: ${error}`));
          }
        });
    });
  }

  /**
   * Detect CSV delimiter from buffer sample
   */
  private detectDelimiter(buffer: Buffer, encoding: BufferEncoding): string {
    const sample = buffer.toString(encoding, 0, Math.min(1024, buffer.length));
    const lines = sample.split('\n').slice(0, 5);

    const delimiters = [',', ';', '\t', '|'];
    const scores: Record<string, number> = {};

    for (const delimiter of delimiters) {
      let consistency = 0;

      const counts = lines.map(line => line.split(delimiter).length);
      const firstCount = counts[0] ?? 1;

      for (const count of counts) {
        if (count === firstCount && count > 1) {
          consistency++;
        }
      }

      scores[delimiter] = consistency * firstCount;
    }

    // Return delimiter with highest score
    const keys = Object.keys(scores);
    if (keys.length === 0) return ',';
    return keys.reduce((a, b) => ((scores[a] ?? 0) > (scores[b] ?? 0) ? a : b), ',');
  }

  /**
   * Detect text encoding from buffer
   */
  private detectEncoding(buffer: Buffer): BufferEncoding {
    // Check for BOM
    if (buffer.length >= 3 && buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf) {
      return 'utf8';
    }

    if (buffer.length >= 2 && buffer[0] === 0xff && buffer[1] === 0xfe) {
      return 'utf16le';
    }

    // Simple heuristic: try UTF-8 first, fallback to latin1
    try {
      const text = buffer.toString('utf8');
      // Check for replacement characters which indicate invalid UTF-8
      if (text.includes('\uFFFD')) {
        return 'latin1';
      }
      return 'utf8';
    } catch {
      return 'latin1';
    }
  }

  /**
   * Normalize column headers
   */
  private normalizeHeaders(headers: string[]): string[] {
    const normalized: string[] = [];
    const seen = new Set<string>();

    for (let i = 0; i < headers.length; i++) {
      let header = headers[i] || `Column_${i + 1}`;

      // Clean header name
      header = header
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '_') // Replace spaces with underscores
        .toLowerCase();

      // Ensure uniqueness
      let uniqueHeader = header;
      let counter = 1;
      while (seen.has(uniqueHeader)) {
        uniqueHeader = `${header}_${counter}`;
        counter++;
      }

      seen.add(uniqueHeader);
      normalized.push(uniqueHeader);
    }

    return normalized;
  }

  /**
   * Process individual row data
   */
  private processRow(row: Record<string, unknown>, headers: string[]): Record<string, unknown> {
    const processed: Record<string, unknown> = {};

    for (const header of headers) {
      const value = row[header];

      // Handle null/undefined values
      if (value === null || value === undefined || value === '') {
        processed[header] = null;
        continue;
      }

      // Convert string values
      if (typeof value === 'string') {
        const trimmedValue = value.trim();

        // Try to parse as number
        if (this.isNumeric(trimmedValue)) {
          processed[header] = parseFloat(trimmedValue);
        }
        // Try to parse as boolean
        else if (this.isBoolean(trimmedValue)) {
          processed[header] = this.parseBoolean(trimmedValue);
        }
        // Try to parse as date
        else if (this.isDate(trimmedValue)) {
          processed[header] = this.parseDate(trimmedValue);
        }
        // Keep as string
        else {
          processed[header] = trimmedValue;
        }
      } else {
        processed[header] = value;
      }
    }

    return processed;
  }

  /**
   * Analyze columns to determine data types and statistics
   */
  private analyzeColumns(rows: Record<string, unknown>[], headers: string[]): CSVColumnMetadata[] {
    const columns: CSVColumnMetadata[] = [];

    for (let i = 0; i < headers.length; i++) {
      const header = headers[i];
      if (!header) continue;

      const values = rows.map(row => row[header]).filter(v => v !== null && v !== undefined);
      const uniqueValues = new Set(values);

      // Determine data type
      const dataType = this.inferDataType(values);

      // Get sample values
      const sampleValues = Array.from(uniqueValues).slice(0, 10);

      columns.push({
        name: header,
        originalName: header, // Could be different if normalized
        index: i,
        dataType,
        nullCount: rows.length - values.length,
        uniqueCount: uniqueValues.size,
        sampleValues,
        hasNulls: rows.length > values.length,
        isUnique: uniqueValues.size === rows.length,
      });
    }

    return columns;
  }

  /**
   * Infer data type from column values
   */
  private inferDataType(values: unknown[]): 'string' | 'number' | 'date' | 'boolean' | 'mixed' {
    if (values.length === 0) return 'string';

    const sample = values.slice(0, this.sampleSize);
    const types = new Set<string>();

    for (const value of sample) {
      if (typeof value === 'number') {
        types.add('number');
      } else if (typeof value === 'boolean') {
        types.add('boolean');
      } else if (value instanceof Date) {
        types.add('date');
      } else if (typeof value === 'string') {
        if (this.isNumeric(value)) {
          types.add('number');
        } else if (this.isBoolean(value)) {
          types.add('boolean');
        } else if (this.isDate(value)) {
          types.add('date');
        } else {
          types.add('string');
        }
      } else {
        types.add('string');
      }
    }

    if (types.size === 1) {
      return Array.from(types)[0] as 'string' | 'number' | 'date' | 'boolean';
    } else if (types.size === 2 && types.has('number') && types.has('string')) {
      // If mostly numbers with some strings, consider it mixed
      const numberCount = sample.filter(
        v => typeof v === 'number' || (typeof v === 'string' && this.isNumeric(v))
      ).length;
      return numberCount > sample.length * 0.8 ? 'number' : 'mixed';
    }

    return 'mixed';
  }

  /**
   * Check if string represents a numeric value
   */
  private isNumeric(value: string): boolean {
    if (!value || value.trim() === '') return false;

    // Remove common number formatting
    const cleaned = value.replace(/[,\s]/g, '');

    // Check for valid number patterns
    return !isNaN(Number(cleaned)) && isFinite(Number(cleaned));
  }

  /**
   * Check if string represents a boolean value
   */
  private isBoolean(value: string): boolean {
    const lower = value.toLowerCase().trim();
    return ['true', 'false', 'yes', 'no', '1', '0', 'y', 'n'].includes(lower);
  }

  /**
   * Parse boolean from string
   */
  private parseBoolean(value: string): boolean {
    const lower = value.toLowerCase().trim();
    return ['true', 'yes', '1', 'y'].includes(lower);
  }

  /**
   * Check if string represents a date
   */
  private isDate(value: string): boolean {
    if (!value || value.trim() === '') return false;

    // Common date patterns
    const datePatterns = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
      /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
      /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
    ];

    return datePatterns.some(pattern => pattern.test(value.trim())) && !isNaN(Date.parse(value));
  }

  /**
   * Parse date from string with multiple format support
   */
  private parseDate(value: string): Date | string {
    try {
      const date = new Date(value);
      return isNaN(date.getTime()) ? value : date;
    } catch {
      return value;
    }
  }

  /**
   * Convert parsing result to Table format
   */
  private convertToTable(parsingResult: CSVParsingResult): Table | null {
    if (parsingResult.rows.length === 0) return null;

    const tableRows: TableRow[] = parsingResult.rows.map((row, rowIndex) => ({
      cells: parsingResult.headers.map((header, colIndex) => {
        const value = row[header];
        let stringValue = '';
        if (value !== null && value !== undefined) {
          if (typeof value === 'object') {
            stringValue = JSON.stringify(value);
          } else {
            stringValue = String(value);
          }
        }
        return {
          value: stringValue,
          type: this.getCellType(value) as 'text' | 'number' | 'date' | 'boolean',
          bounds: {
            x: colIndex * 100, // Approximate cell width
            y: rowIndex * 20, // Approximate cell height
            width: 100,
            height: 20,
            pageNumber: 1,
          },
        };
      }),
    }));

    return {
      id: `csv_table_${Date.now()}`,
      pageNumber: 1,
      bounds: {
        x: 0,
        y: 0,
        width: parsingResult.headers.length * 100,
        height: parsingResult.rows.length * 20,
        pageNumber: 1,
      },
      rows: tableRows,
      headers: parsingResult.headers,
    };
  }

  /**
   * Get cell type for table conversion
   */
  private getCellType(value: unknown): string {
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (value instanceof Date) return 'date';
    return 'text';
  }

  /**
   * Calculate confidence score for table extraction
   */
  private calculateTableConfidence(parsingResult: CSVParsingResult): number {
    let confidence = 1.0;

    // Reduce confidence based on errors
    if (parsingResult.metadata.errors.length > 0) {
      const errorRate = parsingResult.metadata.errors.length / parsingResult.metadata.totalRows;
      confidence -= errorRate * 0.5;
    }

    // Reduce confidence for very small datasets
    if (parsingResult.metadata.totalRows < 2) {
      confidence *= 0.5;
    }

    // Reduce confidence for datasets with too many columns
    if (parsingResult.metadata.totalColumns > this.maxColumnsToProcess) {
      confidence *= 0.7;
    }

    return Math.max(0.1, confidence);
  }

  /**
   * Generate warnings for parsing result
   */
  private generateWarnings(
    rows: Record<string, unknown>[],
    columns: CSVColumnMetadata[],
    errors: CSVParsingError[]
  ): string[] {
    const warnings: string[] = [];

    // Check for high null rates
    columns.forEach(column => {
      const nullRate = column.nullCount / rows.length;
      if (nullRate > 0.5) {
        warnings.push(
          `Column '${column.name}' has high null rate: ${(nullRate * 100).toFixed(1)}%`
        );
      }
    });

    // Check for parsing errors
    if (errors.length > 0) {
      warnings.push(`${errors.length} parsing errors encountered`);
    }

    // Check for very wide datasets
    if (columns.length > 50) {
      warnings.push(`Dataset has many columns (${columns.length}), consider splitting`);
    }

    // Check for very long datasets
    if (rows.length > 100000) {
      warnings.push(`Dataset has many rows (${rows.length}), processing may be slow`);
    }

    return warnings;
  }

  /**
   * Validate CSV data and detect issues
   */
  public async validateCSVData(
    buffer: Buffer,
    options: Partial<CSVProcessingOptions> = {}
  ): Promise<CSVValidationResult> {
    try {
      const parsingResult = await this.parseCSV(buffer, options);

      const duplicateRows: number[] = [];
      const emptyRows: number[] = [];
      const malformedRows: number[] = [];

      // Check for duplicates
      const rowHashes = new Set<string>();
      parsingResult.rows.forEach((row, index) => {
        const rowHash = JSON.stringify(row);
        if (rowHashes.has(rowHash)) {
          duplicateRows.push(index);
        } else {
          rowHashes.add(rowHash);
        }

        // Check for empty rows
        const hasData = Object.values(row).some(
          value => value !== null && value !== undefined && value !== ''
        );
        if (!hasData) {
          emptyRows.push(index);
        }
      });

      // Malformed rows are already captured in parsing errors
      parsingResult.metadata.errors.forEach(error => {
        if (error.row && !malformedRows.includes(error.row)) {
          malformedRows.push(error.row);
        }
      });

      return {
        isValid: parsingResult.metadata.errors.length === 0,
        errors: parsingResult.metadata.errors,
        warnings: parsingResult.metadata.warnings,
        duplicateRows,
        emptyRows,
        malformedRows,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [
          {
            row: 0,
            message: `Validation failed: ${error}`,
          },
        ],
        warnings: [],
        duplicateRows: [],
        emptyRows: [],
        malformedRows: [],
      };
    }
  }

  /**
   * Clean and normalize CSV data
   */
  public async cleanAndNormalizeData(
    buffer: Buffer,
    options: Partial<CSVProcessingOptions> = {}
  ): Promise<CSVParsingResult> {
    const parsingResult = await this.parseCSV(buffer, options);

    // Clean data
    const cleanedRows = parsingResult.rows.map(row => {
      const cleaned: Record<string, unknown> = {};

      for (const [key, value] of Object.entries(row)) {
        if (typeof value === 'string') {
          // Trim whitespace and normalize case
          let cleanValue = value.trim();

          // Remove extra whitespace
          cleanValue = cleanValue.replace(/\s+/g, ' ');

          // Normalize common variations
          cleanValue = this.normalizeTextValue(cleanValue);

          cleaned[key] = cleanValue;
        } else {
          cleaned[key] = value;
        }
      }

      return cleaned;
    });

    return {
      ...parsingResult,
      rows: cleanedRows,
    };
  }

  /**
   * Normalize text values
   */
  private normalizeTextValue(value: string): string {
    // Common normalizations
    const normalizations: Record<string, string> = {
      yes: 'Yes',
      no: 'No',
      true: 'True',
      false: 'False',
      'n/a': 'N/A',
      na: 'N/A',
      null: 'NULL',
      undefined: 'NULL',
    };

    const lower = value.toLowerCase();
    return normalizations[lower] || value;
  }

  /**
   * Remove duplicate rows from CSV data
   */
  public async deduplicateData(
    buffer: Buffer,
    options: Partial<CSVProcessingOptions> = {}
  ): Promise<CSVParsingResult> {
    const parsingResult = await this.parseCSV(buffer, options);

    const uniqueRows: Record<string, unknown>[] = [];
    const seen = new Set<string>();

    for (const row of parsingResult.rows) {
      const rowHash = JSON.stringify(row);
      if (!seen.has(rowHash)) {
        seen.add(rowHash);
        uniqueRows.push(row);
      }
    }

    return {
      ...parsingResult,
      rows: uniqueRows,
      metadata: {
        ...parsingResult.metadata,
        totalRows: uniqueRows.length,
        warnings: [
          ...parsingResult.metadata.warnings,
          `Removed ${parsingResult.rows.length - uniqueRows.length} duplicate rows`,
        ],
      },
    };
  }

  /**
   * Calculate summary statistics for numeric columns
   */
  public calculateSummaryStatistics(
    parsingResult: CSVParsingResult
  ): Record<string, ColumnStatistics> {
    const stats: Record<string, ColumnStatistics> = {};

    for (const column of parsingResult.metadata.columns) {
      if (column.dataType === 'number') {
        const values = parsingResult.rows
          .map(row => row[column.name])
          .filter(v => typeof v === 'number' && !isNaN(v)) as number[];

        if (values.length > 0) {
          const sorted = [...values].sort((a, b) => a - b);
          const sum = values.reduce((a, b) => a + b, 0);
          const mean = sum / values.length;

          stats[column.name] = {
            count: values.length,
            uniqueCount: column.uniqueCount,
            nullCount: column.nullCount,
            min: sorted[0] ?? 0,
            max: sorted[sorted.length - 1] ?? 0,
            mean: mean,
            median: this.calculateMedian(sorted),
            sum: sum,
            standardDeviation: this.calculateStandardDeviation(values, mean),
          };
        }
      } else {
        // For non-numeric columns, provide basic stats
        const values = parsingResult.rows
          .map(row => row[column.name])
          .filter(v => v !== null && v !== undefined);

        const mostCommon = this.findMostCommonValue(values);
        stats[column.name] = {
          count: values.length,
          uniqueCount: column.uniqueCount,
          nullCount: column.nullCount,
          ...(mostCommon && { mostCommon }),
        };
      }
    }

    return stats;
  }

  /**
   * Calculate median value
   */
  private calculateMedian(sortedValues: number[]): number {
    if (sortedValues.length === 0) return 0;
    const mid = Math.floor(sortedValues.length / 2);
    if (sortedValues.length % 2 === 0) {
      const left = sortedValues[mid - 1];
      const right = sortedValues[mid];
      return left !== undefined && right !== undefined ? (left + right) / 2 : 0;
    } else {
      const value = sortedValues[mid];
      return value ?? 0;
    }
  }

  /**
   * Calculate standard deviation
   */
  private calculateStandardDeviation(values: number[], mean: number): number {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  /**
   * Find most common value in array
   */
  private findMostCommonValue(values: unknown[]): { value: unknown; count: number } | null {
    if (values.length === 0) return null;

    const counts = new Map<unknown, number>();
    for (const value of values) {
      counts.set(value, (counts.get(value) || 0) + 1);
    }

    let maxCount = 0;
    let mostCommon: unknown = null;

    for (const [value, count] of counts.entries()) {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = value;
      }
    }

    return mostCommon !== null ? { value: mostCommon, count: maxCount } : null;
  }
}
