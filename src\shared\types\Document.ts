// Document-related type definitions

export interface Document {
  id: string;
  name: string;
  path: string;
  type: DocumentType;
  size: number;
  createdAt: Date;
  updatedAt: Date;
  metadata: DocumentMetadata;
  content?: DocumentContent;
}

export enum DocumentType {
  PDF = 'pdf',
  WORD = 'word',
  EXCEL = 'excel',
  CSV = 'csv',
  IMAGE = 'image',
  TEXT = 'text',
  UNKNOWN = 'unknown',
}

export interface DocumentMetadata {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  pageCount?: number;
  language?: string;
  encoding?: string;
  hasFormFields?: boolean;
  isEncrypted?: boolean;
  permissions?: DocumentPermissions;
}

export interface DocumentPermissions {
  canPrint: boolean;
  canModify: boolean;
  canCopy: boolean;
  canAnnotate: boolean;
  canFillForms: boolean;
}

export interface DocumentContent {
  text: string;
  pages: DocumentPage[];
  formFields?: FormField[];
  images?: DocumentImage[];
  tables?: DocumentTable[];
}

export interface DocumentPage {
  pageNumber: number;
  text: string;
  width: number;
  height: number;
  annotations?: Annotation[];
}

export interface FormField {
  id: string;
  name: string;
  type: FormFieldType;
  value: string | number | boolean;
  required: boolean;
  readonly: boolean;
  bounds: FieldBounds;
  options?: string[];
}

export enum FormFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SELECT = 'select',
  SIGNATURE = 'signature',
  BUTTON = 'button',
}

export interface FieldBounds {
  x: number;
  y: number;
  width: number;
  height: number;
  pageNumber: number;
}

export interface DocumentImage {
  id: string;
  name: string;
  type: string;
  width: number;
  height: number;
  data: ArrayBuffer;
  pageNumber: number;
  bounds: FieldBounds;
}

export interface DocumentTable {
  id: string;
  pageNumber: number;
  bounds: FieldBounds;
  rows: TableRow[];
  headers?: string[];
}

export interface TableRow {
  cells: TableCell[];
}

export interface TableCell {
  value: string;
  type: 'text' | 'number' | 'date' | 'boolean';
  bounds: FieldBounds;
}

export interface Annotation {
  id: string;
  type: AnnotationType;
  content: string;
  author: string;
  createdAt: Date;
  bounds: FieldBounds;
  color?: string;
}

export enum AnnotationType {
  HIGHLIGHT = 'highlight',
  NOTE = 'note',
  COMMENT = 'comment',
  SIGNATURE = 'signature',
  STAMP = 'stamp',
  DRAWING = 'drawing',
}

export interface ProcessingOptions {
  extractText: boolean;
  extractImages: boolean;
  extractTables: boolean;
  detectFormFields: boolean;
  performOCR: boolean;
  ocrLanguage?: string;
  enhanceImages: boolean;
  preserveFormatting: boolean;
  includeHiddenContent?: boolean;
}

export interface ImageProcessingOptions {
  enhanceForOCR: boolean;
  upscaleRatio?: number;
  removeNoise: boolean;
  adjustContrast: boolean;
  adjustBrightness: boolean;
  sharpen: boolean;
  deskew: boolean;
  cropToContent: boolean;
  outputFormat?: 'png' | 'jpeg' | 'tiff' | 'webp';
  quality?: number;
  preserveMetadata: boolean;
}

export interface ProcessingResult {
  documentId: string;
  success: boolean;
  error?: string;
  content: DocumentContent;
  processingTime: number;
  confidence: number;
  warnings: string[];
}

// Additional interfaces for task 2.1

export interface ExtractedData {
  id: string;
  documentId: string;
  type: ExtractedDataType;
  content: any;
  confidence: number;
  extractionMethod: ExtractionMethod;
  coordinates?: DocumentCoordinates;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export enum ExtractedDataType {
  TEXT = 'text',
  TABLE = 'table',
  FORM_FIELD = 'form_field',
  IMAGE = 'image',
  ENTITY = 'entity',
  RELATIONSHIP = 'relationship',
  CALCULATION = 'calculation',
}

export enum ExtractionMethod {
  OCR = 'ocr',
  PDF_PARSER = 'pdf_parser',
  AI_ANALYSIS = 'ai_analysis',
  MANUAL = 'manual',
  TEMPLATE_MATCHING = 'template_matching',
  EXCEL_PARSER = 'excel_parser',
  CSV_PARSER = 'csv_parser',
}

export interface ProcessedDocument {
  document: Document;
  extractedData: ExtractedData[];
  processingResult: ProcessingResult;
  knowledgeEntries: string[]; // IDs of created knowledge entries
  templateMatches: TemplateMatch[];
  validationResults: ValidationResult[];
}

export interface TemplateMatch {
  templateId: string;
  confidence: number;
  matchedFields: FieldMapping[];
  unmatchedFields: string[];
}

export interface ValidationResult {
  fieldId: string;
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface CalculationResult {
  formula: string;
  result: number | string | boolean;
  cellReference?: string;
  dependencies?: string[];
  variables?: string[];
  confidence: number;
}

export interface ValidationError {
  code: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning {
  code: string;
  message: string;
  suggestion?: string;
}

export interface FieldMapping {
  sourceField: string;
  targetField: string;
  transformation?: DataTransformation;
  confidence: number;
  coordinates?: DocumentCoordinates;
  validationRules?: ValidationRule[];
}

export interface DataTransformation {
  type: TransformationType;
  parameters: Record<string, any>;
  description: string;
}

export enum TransformationType {
  FORMAT_DATE = 'format_date',
  PARSE_NUMBER = 'parse_number',
  NORMALIZE_TEXT = 'normalize_text',
  CALCULATE = 'calculate',
  LOOKUP = 'lookup',
  CONCATENATE = 'concatenate',
  EXTRACT_PATTERN = 'extract_pattern',
}

export interface FormTemplate {
  id: string;
  name: string;
  documentType: DocumentType;
  version: string;
  description?: string;
  fieldMappings: FieldMapping[];
  coordinateMappings: CoordinateMapping[];
  variables: TemplateVariable[];
  validationRules: ValidationRule[];
  metadata: TemplateMetadata;
  createdAt: Date;
  updatedAt: Date;
  lastUsed?: Date;
}

export interface TemplateVariable {
  name: string;
  type: VariableType;
  defaultValue?: any;
  description?: string;
  required: boolean;
  constraints?: VariableConstraints;
}

export enum VariableType {
  STRING = 'string',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
}

export interface VariableConstraints {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  minValue?: number;
  maxValue?: number;
  allowedValues?: any[];
}

export interface TemplateMetadata {
  author: string;
  tags: string[];
  category: string;
  usageCount: number;
  successRate: number;
  averageProcessingTime: number;
}

export interface ValidationRule {
  id: string;
  name: string;
  type: ValidationRuleType;
  condition: ValidationCondition;
  message: string;
  severity: 'error' | 'warning' | 'info';
  enabled: boolean;
}

export enum ValidationRuleType {
  REQUIRED = 'required',
  FORMAT = 'format',
  RANGE = 'range',
  PATTERN = 'pattern',
  CUSTOM = 'custom',
  DEPENDENCY = 'dependency',
  CALCULATION = 'calculation',
}

export interface ValidationCondition {
  field: string;
  operator: ValidationOperator;
  value: any;
  parameters?: Record<string, any>;
}

export enum ValidationOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  MATCHES = 'matches',
  IS_EMPTY = 'is_empty',
  IS_NOT_EMPTY = 'is_not_empty',
}

export interface DocumentCoordinates {
  x: number;
  y: number;
  width: number;
  height: number;
  pageNumber: number;
  rotation?: number;
  scale?: number;
}

export interface CoordinateMapping {
  id: string;
  name: string;
  sourceCoordinates: DocumentCoordinates;
  targetField: string;
  fieldType: FormFieldType;
  confidence: number;
  isActive: boolean;
  metadata?: Record<string, any>;
}

export interface OCRResult {
  text: string;
  confidence: number;
  words: OCRWord[];
  lines: OCRLine[];
  paragraphs: OCRParagraph[];
  language: string;
  processingTime: number;
  imageEnhancements: ImageEnhancement[];
}

export interface OCRWord {
  text: string;
  confidence: number;
  bounds: DocumentCoordinates;
  fontSize?: number;
  fontFamily?: string;
  isBold?: boolean;
  isItalic?: boolean;
}

export interface OCRLine {
  text: string;
  confidence: number;
  bounds: DocumentCoordinates;
  words: OCRWord[];
  baseline?: number;
}

export interface OCRParagraph {
  text: string;
  confidence: number;
  bounds: DocumentCoordinates;
  lines: OCRLine[];
  alignment?: TextAlignment;
}

export enum TextAlignment {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  JUSTIFY = 'justify',
}

export interface OCROptions {
  language: string;
  pageSegmentationMode: PageSegmentationMode;
  ocrEngineMode: OCREngineMode;
  whitelist?: string;
  blacklist?: string;
  preserveInterwordSpaces: boolean;
  minimumConfidence: number;
  enhanceImage: boolean;
  deskew: boolean;
  removeNoise: boolean;
}

export enum PageSegmentationMode {
  OSD_ONLY = 0,
  AUTO_OSD = 1,
  AUTO_ONLY = 2,
  AUTO = 3,
  SINGLE_COLUMN = 4,
  SINGLE_BLOCK_VERT_TEXT = 5,
  SINGLE_BLOCK = 6,
  SINGLE_LINE = 7,
  SINGLE_WORD = 8,
  CIRCLE_WORD = 9,
  SINGLE_CHAR = 10,
  SPARSE_TEXT = 11,
  SPARSE_TEXT_OSD = 12,
  RAW_LINE = 13,
}

export enum OCREngineMode {
  LEGACY_ONLY = 0,
  NEURAL_NETS_LSTM_ONLY = 1,
  LEGACY_PLUS_LSTM = 2,
  DEFAULT = 3,
  TESSERACT_ONLY = 0, // Alias for LEGACY_ONLY
  LSTM_ONLY = 1, // Alias for NEURAL_NETS_LSTM_ONLY
}

export interface ImageEnhancement {
  type: EnhancementType;
  parameters: Record<string, any>;
  appliedAt: Date;
  processingTime: number;
}

export enum EnhancementType {
  UPSCALE = 'upscale',
  DENOISE = 'denoise',
  SHARPEN = 'sharpen',
  CONTRAST = 'contrast',
  BRIGHTNESS = 'brightness',
  DESKEW = 'deskew',
  CROP = 'crop',
  ROTATE = 'rotate',
}

export interface ImageProcessResult {
  originalImage: ImageData;
  processedImage: ImageData;
  enhancements: ImageEnhancement[];
  ocrResult?: OCRResult;
  detectedFeatures: DetectedFeature[];
  processingTime: number;
  success: boolean;
  error?: string;
}

export interface ImageData {
  width: number;
  height: number;
  format: string;
  data: ArrayBuffer;
  dpi?: number;
  colorSpace?: string;
}

export interface DetectedFeature {
  type: FeatureType;
  bounds: DocumentCoordinates;
  confidence: number;
  properties: Record<string, unknown>;
}

export enum FeatureType {
  TEXT_BLOCK = 'text_block',
  TABLE = 'table',
  FORM_FIELD = 'form_field',
  SIGNATURE = 'signature',
  BARCODE = 'barcode',
  QR_CODE = 'qr_code',
  LOGO = 'logo',
  IMAGE = 'image',
}
