import { DocumentType } from '../../shared/types/Document';
import {
  DocumentProcessingError,
  DocumentProcessor,
  ProcessorCapabilities,
} from './DocumentProcessor';
import { logger } from '../utils/logger';

/**
 * Processor registration interface
 */
export interface ProcessorRegistration {
  processorClass: new () => DocumentProcessor;
  capabilities: ProcessorCapabilities;
  priority: number; // Higher priority processors are preferred
  isEnabled: boolean;
  metadata: ProcessorMetadata;
}

/**
 * Processor metadata for feature matrix and selection
 */
export interface ProcessorMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  supportedFormats: string[];
  performanceRating: number; // 1-10 scale
  accuracyRating: number; // 1-10 scale
  resourceUsage: ResourceUsage;
  dependencies: string[];
  lastUpdated: Date;
}

/**
 * Resource usage information for processor selection
 */
export interface ResourceUsage {
  memoryUsage: 'low' | 'medium' | 'high';
  cpuUsage: 'low' | 'medium' | 'high';
  diskUsage: 'low' | 'medium' | 'high';
  networkRequired: boolean;
  estimatedProcessingTime: number; // seconds per MB
}

/**
 * Processor selection criteria
 */
export interface ProcessorSelectionCriteria {
  documentType: DocumentType;
  fileSize: number;
  requiresOCR?: boolean;
  requiresFormFields?: boolean;
  requiresTableExtraction?: boolean;
  requiresImageExtraction?: boolean;
  prioritizeSpeed?: boolean;
  prioritizeAccuracy?: boolean;
  maxMemoryUsage?: 'low' | 'medium' | 'high';
  allowNetworkAccess?: boolean;
}

/**
 * Feature matrix for processor capabilities comparison
 */
export interface FeatureMatrix {
  processorName: string;
  documentTypes: DocumentType[];
  features: {
    textExtraction: boolean;
    imageExtraction: boolean;
    tableExtraction: boolean;
    formFieldDetection: boolean;
    ocrCapability: boolean;
    formatPreservation: boolean;
    batchProcessing: boolean;
    realTimeProcessing: boolean;
  };
  performance: {
    speed: number; // 1-10 scale
    accuracy: number; // 1-10 scale
    reliability: number; // 1-10 scale
  };
  limitations: {
    maxFileSize: number;
    supportedEncodings: string[];
    requiresNetwork: boolean;
    memoryIntensive: boolean;
  };
}

/**
 * Processor cache entry for performance optimization
 */
interface ProcessorCacheEntry {
  processor: DocumentProcessor;
  lastUsed: Date;
  usageCount: number;
  documentType: DocumentType;
  createdAt: Date;
}

/**
 * Factory class for creating and managing document processors
 * Implements factory method pattern with processor registration and caching
 */
export class DocumentProcessorFactory {
  private static instance: DocumentProcessorFactory;
  private readonly registeredProcessors: Map<string, ProcessorRegistration> = new Map();
  private readonly processorCache: Map<string, ProcessorCacheEntry> = new Map();
  private readonly maxCacheSize: number = 10;
  private readonly cacheExpirationTime: number = 30 * 60 * 1000; // 30 minutes

  /**
   * Singleton pattern implementation
   */
  public static getInstance(): DocumentProcessorFactory {
    if (!DocumentProcessorFactory.instance) {
      DocumentProcessorFactory.instance = new DocumentProcessorFactory();
    }
    return DocumentProcessorFactory.instance;
  }

  private constructor() {
    // Initialize with default processors (to be implemented in future tasks)
    this.initializeDefaultProcessors();

    // Set up cache cleanup interval
    setInterval(() => this.cleanupCache(), 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Register a new document processor
   */
  public registerProcessor(processorId: string, registration: ProcessorRegistration): void {
    if (this.registeredProcessors.has(processorId)) {
      throw new DocumentProcessingError(
        `Processor with ID '${processorId}' is already registered`,
        'PROCESSOR_ALREADY_REGISTERED'
      );
    }

    // Validate processor registration
    this.validateProcessorRegistration(registration);

    this.registeredProcessors.set(processorId, registration);
  }

  /**
   * Unregister a document processor
   */
  public unregisterProcessor(processorId: string): boolean {
    if (!this.registeredProcessors.has(processorId)) {
      return false;
    }

    // Remove from cache if present
    this.processorCache.delete(processorId);

    return this.registeredProcessors.delete(processorId);
  }

  /**
   * Get all registered processors
   */
  public getRegisteredProcessors(): Map<string, ProcessorRegistration> {
    return new Map(this.registeredProcessors);
  }

  /**
   * Get processor capabilities for a specific processor
   */
  public getProcessorCapabilities(processorId: string): ProcessorCapabilities | null {
    const registration = this.registeredProcessors.get(processorId);
    return registration ? registration.capabilities : null;
  }

  /**
   * Create processor instance based on document type and requirements
   */
  public async createProcessor(criteria: ProcessorSelectionCriteria): Promise<DocumentProcessor> {
    const selectedProcessorId = this.selectBestProcessor(criteria);

    if (!selectedProcessorId) {
      throw new DocumentProcessingError(
        `No suitable processor found for document type: ${criteria.documentType}`,
        'NO_SUITABLE_PROCESSOR',
        undefined,
        'processor_selection'
      );
    }

    return this.getOrCreateProcessor(selectedProcessorId, criteria.documentType);
  }

  /**
   * Create processor instance by specific processor ID
   */
  public async createProcessorById(processorId: string): Promise<DocumentProcessor> {
    const registration = this.registeredProcessors.get(processorId);

    if (!registration) {
      throw new DocumentProcessingError(
        `Processor with ID '${processorId}' not found`,
        'PROCESSOR_NOT_FOUND'
      );
    }

    if (!registration.isEnabled) {
      throw new DocumentProcessingError(
        `Processor '${processorId}' is disabled`,
        'PROCESSOR_DISABLED'
      );
    }

    return this.getOrCreateProcessor(processorId, DocumentType.UNKNOWN);
  }

  /**
   * Get feature matrix for all registered processors
   */
  public getFeatureMatrix(): FeatureMatrix[] {
    const matrix: FeatureMatrix[] = [];

    for (const [processorId, registration] of this.registeredProcessors) {
      if (!registration.isEnabled) continue;

      const capabilities = registration.capabilities;
      const metadata = registration.metadata;

      matrix.push({
        processorName: metadata.name,
        documentTypes: capabilities.supportedTypes,
        features: {
          textExtraction: capabilities.canExtractText,
          imageExtraction: capabilities.canExtractImages,
          tableExtraction: capabilities.canExtractTables,
          formFieldDetection: capabilities.canDetectFormFields,
          ocrCapability: capabilities.canPerformOCR,
          formatPreservation: capabilities.canPreserveFormatting,
          batchProcessing: true, // All processors support batch processing
          realTimeProcessing: metadata.resourceUsage.estimatedProcessingTime < 5,
        },
        performance: {
          speed: this.calculateSpeedRating(metadata.resourceUsage.estimatedProcessingTime),
          accuracy: metadata.accuracyRating,
          reliability: metadata.performanceRating,
        },
        limitations: {
          maxFileSize: capabilities.maxFileSize,
          supportedEncodings: capabilities.supportedEncodings,
          requiresNetwork: capabilities.requiresNetwork,
          memoryIntensive: metadata.resourceUsage.memoryUsage === 'high',
        },
      });
    }

    return matrix;
  }

  /**
   * Get processors that can handle a specific document type
   */
  public getProcessorsForDocumentType(documentType: DocumentType): string[] {
    const compatibleProcessors: string[] = [];

    for (const [processorId, registration] of this.registeredProcessors) {
      if (
        registration.isEnabled &&
        registration.capabilities.supportedTypes.includes(documentType)
      ) {
        compatibleProcessors.push(processorId);
      }
    }

    // Sort by priority (descending)
    return compatibleProcessors.sort((a, b) => {
      const priorityA = this.registeredProcessors.get(a)?.priority || 0;
      const priorityB = this.registeredProcessors.get(b)?.priority || 0;
      return priorityB - priorityA;
    });
  }

  /**
   * Check if a processor can handle specific requirements
   */
  public canProcessorHandleRequirements(
    processorId: string,
    criteria: ProcessorSelectionCriteria
  ): boolean {
    const registration = this.registeredProcessors.get(processorId);

    if (!registration || !registration.isEnabled) {
      return false;
    }

    const capabilities = registration.capabilities;
    const metadata = registration.metadata;

    // Check document type support
    if (!capabilities.supportedTypes.includes(criteria.documentType)) {
      return false;
    }

    // Check file size limits
    if (criteria.fileSize > capabilities.maxFileSize) {
      return false;
    }

    // Check specific feature requirements
    if (criteria.requiresOCR && !capabilities.canPerformOCR) {
      return false;
    }

    if (criteria.requiresFormFields && !capabilities.canDetectFormFields) {
      return false;
    }

    if (criteria.requiresTableExtraction && !capabilities.canExtractTables) {
      return false;
    }

    if (criteria.requiresImageExtraction && !capabilities.canExtractImages) {
      return false;
    }

    // Check resource constraints
    if (
      criteria.maxMemoryUsage &&
      this.compareResourceUsage(metadata.resourceUsage.memoryUsage, criteria.maxMemoryUsage) > 0
    ) {
      return false;
    }

    // Check network requirements
    if (!criteria.allowNetworkAccess && capabilities.requiresNetwork) {
      return false;
    }

    return true;
  }

  /**
   * Select the best processor based on criteria
   */
  private selectBestProcessor(criteria: ProcessorSelectionCriteria): string | null {
    const compatibleProcessors = this.getProcessorsForDocumentType(criteria.documentType).filter(
      processorId => this.canProcessorHandleRequirements(processorId, criteria)
    );

    if (compatibleProcessors.length === 0) {
      return null;
    }

    if (compatibleProcessors.length === 1) {
      return compatibleProcessors[0];
    }

    // Score processors based on criteria preferences
    const scoredProcessors = compatibleProcessors.map(processorId => {
      const registration = this.registeredProcessors.get(processorId)!;
      let score = registration.priority;

      // Adjust score based on preferences
      if (criteria.prioritizeSpeed) {
        const speedRating = this.calculateSpeedRating(
          registration.metadata.resourceUsage.estimatedProcessingTime
        );
        score += speedRating * 2;
      }

      if (criteria.prioritizeAccuracy) {
        score += registration.metadata.accuracyRating * 2;
      }

      // Penalize high resource usage if not preferred
      if (registration.metadata.resourceUsage.memoryUsage === 'high') {
        score -= 1;
      }

      if (registration.metadata.resourceUsage.cpuUsage === 'high') {
        score -= 1;
      }

      return { processorId, score };
    });

    // Sort by score (descending) and return the best
    scoredProcessors.sort((a, b) => b.score - a.score);
    return scoredProcessors[0].processorId;
  }

  /**
   * Get or create processor instance with caching
   */
  private async getOrCreateProcessor(
    processorId: string,
    documentType: DocumentType
  ): Promise<DocumentProcessor> {
    const cacheKey = `${processorId}_${documentType}`;
    const cachedEntry = this.processorCache.get(cacheKey);

    // Check if cached processor is still valid
    if (cachedEntry && this.isCacheEntryValid(cachedEntry)) {
      cachedEntry.lastUsed = new Date();
      cachedEntry.usageCount++;
      return cachedEntry.processor;
    }

    // Create new processor instance
    const registration = this.registeredProcessors.get(processorId)!;
    const processor = new registration.processorClass();

    // Cache the processor
    this.cacheProcessor(cacheKey, processor, documentType);

    return processor;
  }

  /**
   * Cache processor instance
   */
  private cacheProcessor(
    cacheKey: string,
    processor: DocumentProcessor,
    documentType: DocumentType
  ): void {
    // Remove oldest entry if cache is full
    if (this.processorCache.size >= this.maxCacheSize) {
      this.removeOldestCacheEntry();
    }

    const cacheEntry: ProcessorCacheEntry = {
      processor,
      lastUsed: new Date(),
      usageCount: 1,
      documentType,
      createdAt: new Date(),
    };

    this.processorCache.set(cacheKey, cacheEntry);
  }

  /**
   * Check if cache entry is still valid
   */
  private isCacheEntryValid(entry: ProcessorCacheEntry): boolean {
    const now = Date.now();
    const entryAge = now - entry.lastUsed.getTime();
    return entryAge < this.cacheExpirationTime;
  }

  /**
   * Remove oldest cache entry
   */
  private removeOldestCacheEntry(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.processorCache) {
      if (entry.lastUsed.getTime() < oldestTime) {
        oldestTime = entry.lastUsed.getTime();
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.processorCache.delete(oldestKey);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.processorCache) {
      if (now - entry.lastUsed.getTime() > this.cacheExpirationTime) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.processorCache.delete(key));
  }

  /**
   * Calculate speed rating from processing time
   */
  private calculateSpeedRating(processingTimePerMB: number): number {
    // Convert processing time to rating (1-10 scale, higher is better)
    if (processingTimePerMB <= 1) return 10;
    if (processingTimePerMB <= 2) return 9;
    if (processingTimePerMB <= 5) return 8;
    if (processingTimePerMB <= 10) return 7;
    if (processingTimePerMB <= 20) return 6;
    if (processingTimePerMB <= 30) return 5;
    if (processingTimePerMB <= 45) return 4;
    if (processingTimePerMB <= 60) return 3;
    if (processingTimePerMB <= 90) return 2;
    return 1;
  }

  /**
   * Compare resource usage levels
   */
  private compareResourceUsage(
    usage1: 'low' | 'medium' | 'high',
    usage2: 'low' | 'medium' | 'high'
  ): number {
    const levels = { low: 1, medium: 2, high: 3 };
    return levels[usage1] - levels[usage2];
  }

  /**
   * Validate processor registration
   */
  private validateProcessorRegistration(registration: ProcessorRegistration): void {
    if (!registration.processorClass) {
      throw new DocumentProcessingError(
        'Processor class is required',
        'INVALID_PROCESSOR_REGISTRATION'
      );
    }

    if (!registration.capabilities) {
      throw new DocumentProcessingError(
        'Processor capabilities are required',
        'INVALID_PROCESSOR_REGISTRATION'
      );
    }

    if (!registration.metadata) {
      throw new DocumentProcessingError(
        'Processor metadata is required',
        'INVALID_PROCESSOR_REGISTRATION'
      );
    }

    if (registration.capabilities.supportedTypes.length === 0) {
      throw new DocumentProcessingError(
        'Processor must support at least one document type',
        'INVALID_PROCESSOR_REGISTRATION'
      );
    }

    if (registration.capabilities.maxFileSize <= 0) {
      throw new DocumentProcessingError(
        'Maximum file size must be greater than 0',
        'INVALID_PROCESSOR_REGISTRATION'
      );
    }
  }

  /**
   * Initialize default processors
   */
  private initializeDefaultProcessors(): void {
    // Import processors dynamically to avoid circular dependencies
    const { PDFProcessor } = require('./PDFProcessor');
    const { EnhancedImageProcessor } = require('./EnhancedImageProcessor');
    const { ExcelProcessor } = require('./ExcelProcessor');
    const { CSVProcessor } = require('./CSVProcessor');

    // Register PDF Processor with OCR capabilities
    this.registerProcessor('pdf-processor', {
      processorClass: PDFProcessor,
      capabilities: {
        supportedTypes: [DocumentType.PDF],
        canExtractText: true,
        canExtractImages: true,
        canExtractTables: true,
        canDetectFormFields: true,
        canPerformOCR: true,
        canPreserveFormatting: true,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        supportedEncodings: ['utf-8', 'latin1'],
        requiresNetwork: false,
        processingTimeEstimate: 3,
      },
      priority: 10,
      isEnabled: true,
      metadata: {
        name: 'PDF Processor with OCR',
        version: '1.0.0',
        description: 'Advanced PDF processor with OCR capabilities for scanned documents',
        author: 'Paperwork Manager',
        supportedFormats: ['pdf'],
        performanceRating: 9,
        accuracyRating: 9,
        resourceUsage: {
          memoryUsage: 'medium',
          cpuUsage: 'medium',
          diskUsage: 'low',
          networkRequired: false,
          estimatedProcessingTime: 3,
        },
        dependencies: ['tesseract.js', 'sharp', 'pdf.js'],
        lastUpdated: new Date(),
      },
    });

    // Register Enhanced Image Processor
    this.registerProcessor('enhanced-image-processor', {
      processorClass: EnhancedImageProcessor,
      capabilities: {
        supportedTypes: [DocumentType.IMAGE],
        canExtractText: true,
        canExtractImages: true,
        canExtractTables: true,
        canDetectFormFields: true,
        canPerformOCR: true,
        canPreserveFormatting: false,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        supportedEncodings: ['utf-8'],
        requiresNetwork: false,
        processingTimeEstimate: 10,
      },
      priority: 10,
      isEnabled: true,
      metadata: {
        name: 'Enhanced Image Processor',
        version: '1.0.0',
        description: 'Advanced image processor with OCR and AI-powered analysis',
        author: 'Paperwork Manager',
        supportedFormats: ['png', 'jpg', 'jpeg', 'tiff', 'bmp', 'webp'],
        performanceRating: 8,
        accuracyRating: 9,
        resourceUsage: {
          memoryUsage: 'high',
          cpuUsage: 'high',
          diskUsage: 'low',
          networkRequired: false,
          estimatedProcessingTime: 10,
        },
        dependencies: ['tesseract.js', 'sharp'],
        lastUpdated: new Date(),
      },
    });

    // Register Excel Processor (existing)
    try {
      this.registerProcessor('excel-processor', {
        processorClass: ExcelProcessor,
        capabilities: {
          supportedTypes: [DocumentType.EXCEL],
          canExtractText: true,
          canExtractImages: false,
          canExtractTables: true,
          canDetectFormFields: false,
          canPerformOCR: false,
          canPreserveFormatting: true,
          maxFileSize: 50 * 1024 * 1024, // 50MB
          supportedEncodings: ['utf-8'],
          requiresNetwork: false,
          processingTimeEstimate: 2,
        },
        priority: 8,
        isEnabled: true,
        metadata: {
          name: 'Excel Processor',
          version: '1.0.0',
          description: 'Excel spreadsheet processor with formula evaluation',
          author: 'Paperwork Manager',
          supportedFormats: ['xlsx', 'xls'],
          performanceRating: 8,
          accuracyRating: 9,
          resourceUsage: {
            memoryUsage: 'medium',
            cpuUsage: 'low',
            diskUsage: 'low',
            networkRequired: false,
            estimatedProcessingTime: 2,
          },
          dependencies: ['exceljs'],
          lastUpdated: new Date(),
        },
      });
    } catch (error) {
      logger.warn('Failed to register Excel processor', { error });
    }

    // Register CSV Processor (existing)
    try {
      this.registerProcessor('csv-processor', {
        processorClass: CSVProcessor,
        capabilities: {
          supportedTypes: [DocumentType.CSV],
          canExtractText: true,
          canExtractImages: false,
          canExtractTables: true,
          canDetectFormFields: false,
          canPerformOCR: false,
          canPreserveFormatting: false,
          maxFileSize: 10 * 1024 * 1024, // 10MB
          supportedEncodings: ['utf-8', 'latin1'],
          requiresNetwork: false,
          processingTimeEstimate: 1,
        },
        priority: 7,
        isEnabled: true,
        metadata: {
          name: 'CSV Processor',
          version: '1.0.0',
          description: 'CSV file processor with encoding detection',
          author: 'Paperwork Manager',
          supportedFormats: ['csv'],
          performanceRating: 9,
          accuracyRating: 9,
          resourceUsage: {
            memoryUsage: 'low',
            cpuUsage: 'low',
            diskUsage: 'low',
            networkRequired: false,
            estimatedProcessingTime: 1,
          },
          dependencies: ['csv-parser'],
          lastUpdated: new Date(),
        },
      });
    } catch (error) {
      logger.warn('Failed to register CSV processor', { error });
    }

    logger.info('Default document processors initialized', {
      registeredProcessors: this.registeredProcessors.size,
    });
  }

  /**
   * Get cache statistics for monitoring
   */
  public getCacheStatistics(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{
      key: string;
      lastUsed: Date;
      usageCount: number;
      documentType: DocumentType;
    }>;
  } {
    const entries = Array.from(this.processorCache.entries()).map(([key, entry]) => ({
      key,
      lastUsed: entry.lastUsed,
      usageCount: entry.usageCount,
      documentType: entry.documentType,
    }));

    const totalUsage = entries.reduce((sum, entry) => sum + entry.usageCount, 0);
    const hitRate = totalUsage > 0 ? (totalUsage - entries.length) / totalUsage : 0;

    return {
      size: this.processorCache.size,
      maxSize: this.maxCacheSize,
      hitRate,
      entries,
    };
  }

  /**
   * Clear processor cache
   */
  public clearCache(): void {
    this.processorCache.clear();
  }

  /**
   * Get processor usage statistics
   */
  public getProcessorStatistics(): Array<{
    processorId: string;
    name: string;
    isEnabled: boolean;
    supportedTypes: DocumentType[];
    usageCount: number;
    lastUsed?: Date;
  }> {
    const stats: Array<{
      processorId: string;
      name: string;
      isEnabled: boolean;
      supportedTypes: DocumentType[];
      usageCount: number;
      lastUsed?: Date;
    }> = [];

    for (const [processorId, registration] of this.registeredProcessors) {
      // Calculate usage from cache
      let usageCount = 0;
      let lastUsed: Date | undefined;

      for (const [, entry] of this.processorCache) {
        if (entry.processor.constructor === registration.processorClass) {
          usageCount += entry.usageCount;
          if (!lastUsed || entry.lastUsed > lastUsed) {
            lastUsed = entry.lastUsed;
          }
        }
      }

      stats.push({
        processorId,
        name: registration.metadata.name,
        isEnabled: registration.isEnabled,
        supportedTypes: registration.capabilities.supportedTypes,
        usageCount,
        lastUsed,
      });
    }

    return stats;
  }
}

// Export singleton instance
export const documentProcessorFactory = DocumentProcessorFactory.getInstance();
